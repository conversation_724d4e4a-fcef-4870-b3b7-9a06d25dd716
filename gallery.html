<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Gallery - GetTwisted Hair Studios | Futuristic Hair Art Showcase</title>

  <!-- SEO & Social Meta -->
  <meta name="description" content="Explore GetTwisted Hair Studios' futuristic gallery showcasing locs, braids, and barber services. View our latest transformations and book your appointment.">
  <meta property="og:title" content="Gallery - GetTwisted Hair Studios | Futuristic Hair Art Showcase" />
  <meta property="og:description" content="Discover our stunning collection of hair transformations across locs, braids, and barber services." />
  <meta property="og:image" content="https://www.gettwistedloc.com/assets/images/hero.jpg" />
  <meta property="og:url" content="https://www.gettwistedloc.com/gallery.html" />
  <meta property="og:type" content="website" />

  <!-- Structured Data for SEO -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "ImageGallery",
    "name": "GetTwisted Hair Studios Gallery",
    "description": "Professional hair styling gallery featuring locs, braids, and barber services",
    "url": "https://www.gettwistedloc.com/gallery.html",
    "provider": {
      "@type": "Organization",
      "name": "GetTwisted Hair Studios",
      "url": "https://www.gettwistedloc.com"
    }
  }
  </script>

  <!-- Security Headers -->
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; frame-src https://book.squareup.com https://*.square.site/ https://*.squareup.com/; connect-src 'self' http://127.0.0.1:3000 https://*.square.site/ https://*.squareup.com/ https://www.google-analytics.com https://www.googletagmanager.com; img-src 'self' https://*.square.site/ https://*.squareup.com/ https://www.googletagmanager.com data:; object-src 'none';">
  <meta http-equiv="X-Content-Type-Options" content="nosniff">
  <meta http-equiv="Referrer-Policy" content="no-referrer-when-downgrade">
  <meta http-equiv="Permissions-Policy" content="geolocation=(), microphone=(), camera=()">

  <!-- Production Tailwind CSS -->
  <link rel="stylesheet" href="assets/css/tailwind-production.css">

  <!-- Futuristic Design System -->
  <link rel="stylesheet" href="assets/css/futuristic.css">

  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&display=swap" rel="stylesheet">

  <!-- Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-QMXCEDX3LX"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'G-QMXCEDX3LX');
  </script>

  <!-- Futuristic CSS Styles -->
  <style>
    :root {
      --neon-purple: #8b5cf6;
      --neon-cyan: #06b6d4;
      --neon-pink: #ec4899;
      --neon-green: #10b981;
      --glass-bg: rgba(255, 255, 255, 0.1);
      --glass-border: rgba(255, 255, 255, 0.2);
    }

    * {
      font-family: 'Exo 2', sans-serif;
    }

    .font-futuristic {
      font-family: 'Orbitron', monospace;
    }

    .glass {
      background: var(--glass-bg);
      backdrop-filter: blur(10px);
      border: 1px solid var(--glass-border);
    }

    .btn-holographic {
      background: linear-gradient(45deg, var(--neon-purple), var(--neon-cyan));
      border: 1px solid var(--glass-border);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .btn-holographic:hover {
      transform: scale(1.05);
      box-shadow: 0 0 30px rgba(139, 92, 246, 0.6);
    }

    .btn-holographic::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    .btn-holographic:hover::before {
      left: 100%;
    }

    .neon-glow {
      text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;
    }

    /* Navigation Logo Styles */
    nav img {
      filter: drop-shadow(0 0 10px rgba(139, 92, 246, 0.5));
      transition: all 0.3s ease;
    }

    nav img:hover {
      filter: drop-shadow(0 0 15px rgba(139, 92, 246, 0.8));
      transform: scale(1.05);
    }

    /* Ensure proper navigation height and spacing */
    nav {
      min-height: 80px;
      backdrop-filter: blur(15px);
    }

    /* Body content spacing to prevent overlap */
    body {
      padding-top: 0;
    }

    .gallery-item {
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .gallery-item:hover {
      transform: translateY(-10px);
      box-shadow: 0 20px 40px rgba(139, 92, 246, 0.3);
    }

    .gallery-overlay {
      background: linear-gradient(135deg, rgba(139, 92, 246, 0.9), rgba(6, 182, 212, 0.9));
      backdrop-filter: blur(5px);
    }

    .filter-btn {
      background: var(--glass-bg);
      border: 1px solid var(--glass-border);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      color: white;
    }

    .filter-btn.active {
      background: linear-gradient(45deg, var(--neon-purple), var(--neon-cyan));
      box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
      border-color: var(--neon-cyan);
    }

    .filter-btn:hover {
      transform: scale(1.05);
      box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
      border-color: var(--neon-cyan);
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .animate-fade-in-up {
      animation: fadeInUp 0.6s ease-out;
    }

    /* Responsive Navigation Styles */
    @media (max-width: 768px) {
      nav {
        padding: 1rem;
        min-height: 70px;
      }

      nav img {
        height: 2.5rem;
      }

      .font-futuristic {
        font-size: 1rem;
      }

      .font-futuristic span {
        font-size: 0.75rem;
      }

      /* Adjust hero section padding for mobile */
      .pt-40 {
        padding-top: 6rem;
      }
    }

    @media (max-width: 480px) {
      nav {
        padding: 0.75rem;
        min-height: 65px;
      }

      nav img {
        height: 2rem;
      }

      .font-futuristic {
        font-size: 0.875rem;
      }

      .font-futuristic span {
        font-size: 0.625rem;
      }
    }

    @keyframes pulse-glow {
      0%, 100% {
        box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
      }
      50% {
        box-shadow: 0 0 30px rgba(139, 92, 246, 0.8);
      }
    }

    .pulse-glow {
      animation: pulse-glow 2s infinite;
    }

    .modal-backdrop {
      background: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(10px);
    }

    .modal-content {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .lazy-load {
      transition: opacity 0.3s ease;
    }

    .lazy-load.loading {
      opacity: 0.5;
    }

    .lazy-load.loaded {
      opacity: 1;
    }

    /* Force modal to stay hidden unless explicitly shown */
    #gallery-modal.hidden {
      display: none !important;
      visibility: hidden !important;
      opacity: 0 !important;
      pointer-events: none !important;
      z-index: -1 !important;
    }

    #gallery-modal.active {
      display: flex !important;
      visibility: visible !important;
      opacity: 1 !important;
      z-index: 50 !important;
    }

    /* Prevent modal flash during page load */
    #gallery-modal:not(.active) {
      display: none !important;
    }
  </style>
</head>

<body class="bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 min-h-screen text-white">
  <!-- Futuristic Navigation -->
  <nav class="glass fixed top-0 left-0 right-0 z-50 px-6 py-4">
    <div class="max-w-7xl mx-auto flex items-center justify-between">
      <!-- Logo -->
      <a href="index.html" class="flex items-center space-x-3">
        <img src="assets/images/gettwisted-logo.png" alt="GetTwisted Hair Studios Logo" class="h-12 w-auto">
        <div class="font-futuristic text-xl font-bold neon-glow text-neon-cyan">
          GETTWISTED
          <span class="block text-sm text-neon-purple">HAIR STUDIOS</span>
        </div>
      </a>

      <!-- Desktop Navigation -->
      <div class="hidden md:flex items-center space-x-8">
        <a href="index.html" class="text-white hover:text-neon-cyan transition-colors duration-300">Home</a>
        <a href="stylists.html" class="text-white hover:text-neon-cyan transition-colors duration-300">Stylists</a>
        <a href="services.html" class="text-white hover:text-neon-cyan transition-colors duration-300">Services</a>
        <a href="gallery.html" class="text-neon-cyan font-semibold neon-glow">Gallery</a>
        <a href="help.html" class="text-white hover:text-neon-cyan transition-colors duration-300">Help</a>
        <button onclick="openGeneralBooking()" class="btn-holographic px-6 py-2 rounded-lg font-semibold">
          <i class="fas fa-calendar-plus mr-2"></i>Book Now
        </button>
      </div>

      <!-- Mobile Menu Button -->
      <button id="mobileMenuBtn" class="md:hidden text-white text-2xl">
        <i class="fas fa-bars"></i>
      </button>
    </div>
  </nav>

  <!-- Mobile Menu -->
  <div id="mobileMenu" class="glass fixed top-20 left-0 right-0 z-40 mx-4 rounded-xl p-6 hidden md:hidden">
    <div class="flex flex-col space-y-4">
      <a href="index.html" class="text-white hover:text-neon-cyan transition-colors duration-300 py-2">Home</a>
      <a href="stylists.html" class="text-white hover:text-neon-cyan transition-colors duration-300 py-2">Stylists</a>
      <a href="services.html" class="text-white hover:text-neon-cyan transition-colors duration-300 py-2">Services</a>
      <a href="gallery.html" class="text-neon-cyan font-semibold py-2 neon-glow">Gallery</a>
      <a href="help.html" class="text-white hover:text-neon-cyan transition-colors duration-300 py-2">Help</a>
      <button onclick="openGeneralBooking()" class="btn-holographic px-6 py-3 rounded-lg font-semibold mt-4">
        <i class="fas fa-calendar-plus mr-2"></i>Book Now
      </button>
    </div>
  </div>

  <!-- Hero Section -->
  <section class="pt-40 pb-16 px-6">
    <div class="max-w-7xl mx-auto text-center">
      <h1 class="font-futuristic text-6xl md:text-8xl font-bold mb-6 neon-glow text-transparent bg-clip-text bg-gradient-to-r from-neon-purple to-neon-cyan animate-fade-in-up">
        GALLERY
      </h1>
      <p class="text-xl md:text-2xl text-gray-300 mb-8 animate-fade-in-up" style="animation-delay: 0.2s;">
        Discover our stunning collection of hair transformations across all three studios
      </p>
      <div class="glass rounded-2xl p-6 max-w-4xl mx-auto animate-fade-in-up" style="animation-delay: 0.4s;">
        <p class="text-lg text-gray-200">
          Explore our portfolio of <span class="text-neon-purple font-semibold">Locs</span>,
          <span class="text-neon-pink font-semibold">Braids</span>, and
          <span class="text-neon-green font-semibold">Barber</span> services
        </p>
      </div>
    </div>
  </section>

  <!-- Filter Controls -->
  <section class="px-6 mb-12">
    <div class="max-w-7xl mx-auto">
      <!-- Shop Category Filters -->
      <div class="mb-8">
        <h2 class="font-futuristic text-2xl font-bold mb-6 text-center neon-glow text-neon-cyan">
          Filter by Shop
        </h2>
        <div class="flex flex-wrap justify-center gap-4">
          <button class="filter-btn active px-6 py-3 rounded-lg font-semibold transition-all duration-300"
                  data-category="all" onclick="filterGallery('all')">
            <i class="fas fa-th-large mr-2"></i>All Shops
          </button>
          <button class="filter-btn px-6 py-3 rounded-lg font-semibold transition-all duration-300"
                  data-category="locs" onclick="filterGallery('locs')">
            <i class="fas fa-crown mr-2"></i>Locs Studio
          </button>
          <button class="filter-btn px-6 py-3 rounded-lg font-semibold transition-all duration-300"
                  data-category="braids" onclick="filterGallery('braids')">
            <i class="fas fa-magic mr-2"></i>Braids Shop
          </button>
          <button class="filter-btn px-6 py-3 rounded-lg font-semibold transition-all duration-300"
                  data-category="barber" onclick="filterGallery('barber')">
            <i class="fas fa-cut mr-2"></i>Barber Shop
          </button>
        </div>
      </div>

      <!-- Stylist Filters -->
      <div class="mb-8">
        <h3 class="font-futuristic text-xl font-bold mb-4 text-center text-neon-purple">
          Filter by Stylist
        </h3>
        <div class="flex flex-wrap justify-center gap-3">
          <button class="filter-btn active px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300"
                  data-stylist="all" onclick="filterByStylist('all')">
            All Stylists
          </button>
          <button class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300"
                  data-stylist="sheryl_williams" onclick="filterByStylist('sheryl_williams')">
            Sheryl Williams
          </button>
          <button class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300"
                  data-stylist="michael_williams" onclick="filterByStylist('michael_williams')">
            Michael Williams
          </button>
          <button class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300"
                  data-stylist="sasha" onclick="filterByStylist('sasha')">
            Sasha
          </button>
          <button class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300"
                  data-stylist="marshall" onclick="filterByStylist('marshall')">
            Marshall
          </button>
          <button class="filter-btn px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300"
                  data-stylist="rachel" onclick="filterByStylist('rachel')">
            Rachel
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Gallery Grid -->
  <section class="px-6 pb-16">
    <div class="max-w-7xl mx-auto">
      <!-- Loading State -->
      <div id="gallery-loading" class="text-center py-16">
        <div class="inline-block animate-spin rounded-full h-16 w-16 border-4 border-neon-cyan border-t-transparent"></div>
        <p class="mt-4 text-neon-cyan font-semibold">Loading Gallery...</p>
      </div>

      <!-- Portfolio Grid -->
      <div id="portfolio-container" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 hidden">
        <!-- Portfolio items will be loaded here by the stylist portfolio manager -->
      </div>

      <!-- Fallback Gallery Grid (for backward compatibility) -->
      <div id="gallery-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 hidden">
        <!-- Gallery items will be dynamically loaded here -->
      </div>

      <!-- No Results Message -->
      <div id="no-results" class="text-center py-16 hidden">
        <div class="glass rounded-2xl p-8 max-w-md mx-auto">
          <i class="fas fa-search text-4xl text-neon-purple mb-4"></i>
          <h3 class="font-futuristic text-xl font-bold mb-2 text-neon-cyan">No Results Found</h3>
          <p class="text-gray-300">Try adjusting your filters to see more gallery items.</p>
          <button onclick="resetFilters()" class="btn-holographic px-6 py-2 rounded-lg mt-4">
            <i class="fas fa-refresh mr-2"></i>Reset Filters
          </button>
        </div>
      </div>
    </div>
  </section>

  <!-- Futuristic Modal -->
  <div id="gallery-modal" class="modal-backdrop fixed inset-0 z-50 hidden items-center justify-center p-4">
    <div class="modal-content rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden relative">
      <!-- Close Button -->
      <button id="close-modal" class="absolute top-4 right-4 z-10 btn-holographic w-12 h-12 rounded-full flex items-center justify-center">
        <i class="fas fa-times text-xl"></i>
      </button>

      <!-- Modal Content -->
      <div class="p-6">
        <!-- Image Container -->
        <div class="relative mb-6">
          <img id="modal-image" src="" alt="" class="w-full h-96 object-cover rounded-xl">

          <!-- Navigation Arrows -->
          <button id="prev-btn" class="absolute left-4 top-1/2 transform -translate-y-1/2 btn-holographic w-12 h-12 rounded-full flex items-center justify-center">
            <i class="fas fa-chevron-left"></i>
          </button>
          <button id="next-btn" class="absolute right-4 top-1/2 transform -translate-y-1/2 btn-holographic w-12 h-12 rounded-full flex items-center justify-center">
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>

        <!-- Service Info -->
        <div class="grid md:grid-cols-2 gap-6">
          <div>
            <h3 id="modal-title" class="font-futuristic text-2xl font-bold mb-2 neon-glow text-neon-cyan"></h3>
            <p id="modal-description" class="text-gray-300 mb-4"></p>
            <div class="flex items-center gap-4 mb-4">
              <span id="modal-stylist" class="glass px-3 py-1 rounded-lg text-sm font-medium"></span>
              <span id="modal-category" class="glass px-3 py-1 rounded-lg text-sm font-medium"></span>
            </div>
          </div>
          <div class="flex flex-col justify-center">
            <div id="modal-price" class="text-3xl font-bold mb-4 neon-glow text-neon-purple"></div>
            <button id="modal-book-btn" class="btn-holographic px-8 py-3 rounded-lg font-semibold w-full">
              <i class="fas fa-calendar-plus mr-2"></i>Book This Service
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer class="glass mt-16 py-12 px-6">
    <div class="max-w-7xl mx-auto">
      <div class="grid md:grid-cols-4 gap-8">
        <!-- Brand -->
        <div class="md:col-span-2">
          <h3 class="font-futuristic text-2xl font-bold mb-4 neon-glow text-neon-cyan">GETTWISTED HAIR STUDIOS</h3>
          <p class="text-gray-300 mb-4">Experience the future of hair artistry with our cutting-edge techniques and futuristic approach to natural hair care.</p>
          <div class="flex space-x-4">
            <a href="#" class="text-neon-purple hover:text-neon-cyan transition-colors duration-300">
              <i class="fab fa-instagram text-2xl"></i>
            </a>
            <a href="#" class="text-neon-purple hover:text-neon-cyan transition-colors duration-300">
              <i class="fab fa-facebook text-2xl"></i>
            </a>
            <a href="#" class="text-neon-purple hover:text-neon-cyan transition-colors duration-300">
              <i class="fab fa-tiktok text-2xl"></i>
            </a>
          </div>
        </div>

        <!-- Quick Links -->
        <div>
          <h4 class="font-semibold mb-4 text-neon-purple">Quick Links</h4>
          <div class="space-y-2">
            <a href="index.html" class="block text-gray-300 hover:text-neon-cyan transition-colors duration-300">Home</a>
            <a href="locs/" class="block text-gray-300 hover:text-neon-cyan transition-colors duration-300">Locs Studio</a>
            <a href="braids/" class="block text-gray-300 hover:text-neon-cyan transition-colors duration-300">Braids Shop</a>
            <a href="barber/" class="block text-gray-300 hover:text-neon-cyan transition-colors duration-300">Barber Shop</a>
          </div>
        </div>

        <!-- Contact -->
        <div>
          <h4 class="font-semibold mb-4 text-neon-purple">Contact</h4>
          <div class="space-y-2 text-gray-300">
            <p><i class="fas fa-phone mr-2 text-neon-cyan"></i>(*************</p>
            <p><i class="fas fa-envelope mr-2 text-neon-cyan"></i><EMAIL></p>
            <button onclick="openGeneralBooking()" class="btn-holographic px-4 py-2 rounded-lg mt-4">
              <i class="fas fa-calendar-plus mr-2"></i>Book Now
            </button>
          </div>
        </div>
      </div>

      <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
        <p>&copy; 2024 GetTwisted Hair Studios. All rights reserved. | Powered by futuristic design.</p>
      </div>
    </div>
  </footer>

  <!-- Scripts -->
  <script src="assets/js/square-booking.js"></script>
  <script src="assets/js/gallery-manager.js"></script>
  <script src="assets/js/stylist-portfolio-manager.js"></script>

  <script>
    // Global variables for filtering
    let portfolioManager;
    let currentCategoryFilter = 'all';
    let currentStylistFilter = 'all';

    // Initialize portfolio system
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize the portfolio manager
      portfolioManager = new StylistPortfolioManager('portfolio-container');

      // Update results count when portfolio loads
      portfolioManager.onPortfolioLoaded = function() {
        updateResultsCount();
      };

      // Update results count when filters change
      portfolioManager.onFilterChange = function() {
        updateResultsCount();
      };
    });

    // Category filter function
    function filterGallery(category) {
      currentCategoryFilter = category;

      // Update active button
      document.querySelectorAll('[data-category]').forEach(btn => {
        btn.classList.remove('active');
      });
      document.querySelector(`[data-category="${category}"]`).classList.add('active');

      // Apply filters to portfolio manager
      if (portfolioManager) {
        portfolioManager.filterByCategory(category);
        updateResultsCount();
      }
    }

    // Stylist filter function
    function filterByStylist(stylistId) {
      currentStylistFilter = stylistId;

      // Update active button
      document.querySelectorAll('[data-stylist]').forEach(btn => {
        btn.classList.remove('active');
      });
      document.querySelector(`[data-stylist="${stylistId}"]`).classList.add('active');

      // Apply filters to portfolio manager
      if (portfolioManager) {
        portfolioManager.filterByStylist(stylistId);
        updateResultsCount();
      }
    }

    // Update results count
    function updateResultsCount() {
      const resultsElement = document.getElementById('results-count');
      if (!resultsElement || !portfolioManager) return;

      const totalWork = portfolioManager.getAllWork().length;
      const filteredWork = portfolioManager.getFilteredWork().length;

      if (currentCategoryFilter === 'all' && currentStylistFilter === 'all') {
        resultsElement.textContent = `Showing all ${totalWork} portfolio items`;
      } else {
        const categoryText = currentCategoryFilter === 'all' ? 'all categories' :
                           portfolioManager.getCategoryName(currentCategoryFilter);
        const stylistText = currentStylistFilter === 'all' ? 'all stylists' :
                          portfolioManager.getStylistName(currentStylistFilter);

        resultsElement.textContent = `Showing ${filteredWork} of ${totalWork} items (${categoryText}, ${stylistText})`;
      }
    }

    // Booking function
    function openGeneralBooking() {
      if (typeof openSquareBooking === 'function') {
        openSquareBooking('general', 'General Booking');
      } else {
        window.open('https://squareup.com/appointments/book/example', '_blank');
      }
    }

    // Gallery Modal Control Functions
    function openGalleryModal(imageData = {}) {
      const modal = document.getElementById("gallery-modal");
      if (!modal) return;

      try {
        // Update modal content if imageData provided
        if (imageData.src) {
          const modalImage = document.getElementById("modal-image");
          const modalTitle = document.getElementById("modal-title");
          const modalDescription = document.getElementById("modal-description");
          const modalStylist = document.getElementById("modal-stylist");
          const modalCategory = document.getElementById("modal-category");

          if (modalImage) {
            modalImage.src = imageData.src;
            modalImage.alt = imageData.alt || '';
          }
          if (modalTitle) modalTitle.textContent = imageData.title || '';
          if (modalDescription) modalDescription.textContent = imageData.description || '';
          if (modalStylist) modalStylist.textContent = imageData.stylist || '';
          if (modalCategory) modalCategory.textContent = imageData.category || '';
        }

        // Show modal
        modal.classList.remove("hidden");
        modal.classList.add("active");
        document.body.style.overflow = "hidden";

        console.log("Gallery modal opened with data:", imageData);
      } catch (error) {
        console.warn("Failed to open gallery modal:", error);
      }
    }

    // Make function globally available
    window.openGalleryModal = openGalleryModal;

    function closeGalleryModal() {
      const modal = document.getElementById("gallery-modal");
      if (!modal) return;

      try {
        modal.classList.add("hidden");
        modal.classList.remove("active");
        document.body.style.overflow = "";

        console.log("Gallery modal closed");
      } catch (error) {
        console.warn("Failed to close gallery modal:", error);
      }
    }

    // Make function globally available
    window.closeGalleryModal = closeGalleryModal;
  </script>

  <script>
    // Futuristic Gallery System
    class FuturisticGallery {
      constructor() {
        this.galleryData = null;
        this.currentFilter = 'all';
        this.currentStylistFilter = 'all';
        this.currentModalIndex = 0;
        this.filteredItems = [];
        this.init();
      }

      async init() {
        try {
          await this.loadGalleryData();
          this.setupEventListeners();
          this.renderGallery();
          this.hideLoading();
          console.log('🚀 Futuristic Gallery initialized');
        } catch (error) {
          console.error('❌ Gallery initialization failed:', error);
          this.showError();
        }
      }

      async loadGalleryData() {
        try {
          const response = await fetch('assets/data/gallery-images.json');
          if (!response.ok) throw new Error('Failed to load gallery data');
          this.galleryData = await response.json();
        } catch (error) {
          console.warn('Using fallback gallery data');
          this.galleryData = this.getFallbackData();
        }
      }

      getFallbackData() {
        return {
          services: {
            'dreadlocks_retwist': {
              category: 'locs',
              name: 'Dreadlocks Retwist',
              price: '$120',
              duration: '1hr 30min',
              stylist: 'Sheryl',
              images: [
                { src: 'assets/images/gallery/sm_retwist.jpg', alt: 'Professional dreadlock retwist service', featured: true }
              ]
            },
            'starter_locs': {
              category: 'locs',
              name: 'Starter Locs',
              price: '$150',
              duration: '2hr',
              stylist: 'Sheryl',
              images: [
                { src: 'assets/images/gallery/rs1.jpg', alt: 'Starter locs installation', featured: true }
              ]
            },
            'box_braids': {
              category: 'braids',
              name: 'Box Braids',
              price: '$200',
              duration: '3hr',
              stylist: 'Sasha',
              images: [
                { src: 'assets/images/gallery/rs3.jpg', alt: 'Beautiful box braids', featured: true }
              ]
            },
            'haircuts': {
              category: 'barber',
              name: 'Professional Haircuts',
              price: '$45',
              duration: '45min',
              stylist: 'Michael',
              images: [
                { src: 'assets/images/gallery/rs4.jpg', alt: 'Professional haircut service', featured: true }
              ]
            }
          }
        };
      }

      setupEventListeners() {
        // Setup lightbox close functionality
        const lightbox = document.getElementById('gallery-lightbox');
        const closeBtn = document.getElementById('close-lightbox');

        if (closeBtn && lightbox) {
          closeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Force close the lightbox
            lightbox.classList.add('hidden');
            lightbox.style.display = 'none';
            lightbox.style.visibility = 'hidden';
            lightbox.style.opacity = '0';
            lightbox.style.pointerEvents = 'none';
            lightbox.style.zIndex = '-1';
            document.body.style.overflow = '';

            console.log("Lightbox closed via direct close button handler");
          });
        }
      }

      renderGallery() {
        // Gallery rendering logic will be handled by portfolio manager
        console.log('Gallery rendering delegated to portfolio manager');
      }

      hideLoading() {
        const loadingElement = document.getElementById('loading-indicator');
        if (loadingElement) {
          loadingElement.style.display = 'none';
        }
      }

      showError() {
        console.error('Gallery failed to load');
        // Show error message to user
      }
    }
  </script>

  <style>
    /* Additional Lightbox Styles */
    #gallery-lightbox {
      display: none;
      position: fixed;
      inset: 0;
      z-index: 9999;
      background-color: rgba(0, 0, 0, 0.9);
      align-items: center;
      justify-content: center;
    }

    #gallery-lightbox.hidden {
      display: none !important;
    }

    #gallery-lightbox:not(.hidden) {
      display: flex !important;
    }

    #prev-image, #next-image, #close-lightbox {
      opacity: 0.9;
      transition: opacity 0.3s ease, transform 0.3s ease, background-color 0.3s ease;
      cursor: pointer !important;
      z-index: 10001 !important;
      pointer-events: auto !important;
      background-color: rgba(219, 39, 119, 0.8); /* Pink-600 with opacity */
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
      position: absolute !important;
      display: block !important;
    }

    #prev-image:hover, #next-image:hover, #close-lightbox:hover {
      opacity: 1;
      transform: scale(1.1);
      background-color: rgba(219, 39, 119, 1); /* Full opacity on hover */
    }

    #prev-image:active, #next-image:active, #close-lightbox:active {
      transform: scale(0.95);
      background-color: rgba(190, 24, 93, 1); /* Pink-700 */
    }

    /* Make sure navigation buttons are always visible */
    #gallery-lightbox:not(.hidden) #prev-image,
    #gallery-lightbox:not(.hidden) #next-image,
    #gallery-lightbox:not(.hidden) #close-lightbox {
      display: block !important;
      visibility: visible !important;
      opacity: 0.9 !important;
      pointer-events: auto !important;
    }

    /* Specific styles for navigation buttons to ensure they're clickable */
    #prev-image {
      left: 4rem !important;
      top: 50% !important;
      transform: translateY(-50%) !important;
      width: 60px !important;
      height: 60px !important;
    }

    #next-image {
      right: 4rem !important;
      top: 50% !important;
      transform: translateY(-50%) !important;
      width: 60px !important;
      height: 60px !important;
    }

    /* Ensure filter buttons are visible and clickable */
    .filter-btn, .stylist-btn {
      position: relative;
      z-index: 1;
      cursor: pointer;
    }
  </style>

  <!-- Additional JavaScript for Gallery Functionality -->
  <script>
    // Extend the FuturisticGallery class with additional methods
    if (typeof FuturisticGallery !== 'undefined') {
      FuturisticGallery.prototype.hideLoading = function() {
        const loading = document.getElementById('gallery-loading');
        if (loading) loading.classList.add('hidden');
      };

      FuturisticGallery.prototype.showError = function() {
        const loading = document.getElementById('gallery-loading');
        if (loading) {
          loading.innerHTML = `
            <div class="glass rounded-2xl p-8 max-w-md mx-auto">
              <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
              <h3 class="font-futuristic text-xl font-bold mb-2 text-red-400">Error Loading Gallery</h3>
              <p class="text-gray-300">Please refresh the page to try again.</p>
            </div>
          `;
        }
      };

      FuturisticGallery.prototype.showNoResults = function() {
        const noResults = document.getElementById('no-results');
        if (noResults) noResults.classList.remove('hidden');

        const grid = document.getElementById('gallery-grid');
        if (grid) grid.classList.add('hidden');
      };

      FuturisticGallery.prototype.hideNoResults = function() {
        const noResults = document.getElementById('no-results');
        if (noResults) noResults.classList.add('hidden');
      };

      FuturisticGallery.prototype.animateItems = function() {
        const items = document.querySelectorAll('.gallery-item');
        items.forEach((item, index) => {
          setTimeout(() => {
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
          }, index * 100);
        });
      };
    }

    // Global filter functions
    function filterGallery(category) {
      if (window.futuristicGallery) {
        window.futuristicGallery.currentFilter = category;
        window.futuristicGallery.renderGallery();

        // Update active button
        document.querySelectorAll('[data-category]').forEach(btn => {
          btn.classList.remove('active');
        });
        document.querySelector(`[data-category="${category}"]`).classList.add('active');
      }
    }

    function filterByStylist(stylist) {
      if (window.futuristicGallery) {
        window.futuristicGallery.currentStylistFilter = stylist;
        window.futuristicGallery.renderGallery();

        // Update active button
        document.querySelectorAll('[data-stylist]').forEach(btn => {
          btn.classList.remove('active');
        });
        document.querySelector(`[data-stylist="${stylist}"]`).classList.add('active');
      }
    }

    function resetFilters() {
      filterGallery('all');
      filterByStylist('all');
    }

    // Initialize gallery when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
      try {
        window.futuristicGallery = new FuturisticGallery();
        console.log('✅ Futuristic Gallery initialized');
      } catch (error) {
        console.warn('Gallery initialization failed:', error);
      }

      // Initialize Square Booking if available (but don't auto-open)
      try {
        if (typeof SquareBookingManager !== 'undefined') {
          window.squareBooking = new SquareBookingManager();
          console.log('✅ Square Booking Manager initialized (manual activation required)');
        }
      } catch (error) {
        console.warn('Square Booking initialization failed:', error);
        // Don't auto-open booking on error
      }

      // Initialize Gallery Modal Event Listeners
      try {
        const modal = document.getElementById("gallery-modal");
        const closeBtn = document.getElementById("close-modal");

        if (modal && closeBtn) {
          // Close button click
          closeBtn.addEventListener('click', closeGalleryModal);

          // Click outside modal to close
          modal.addEventListener('click', function(e) {
            if (e.target === modal) {
              closeGalleryModal();
            }
          });

          // Escape key to close
          document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
              closeGalleryModal();
            }
          });

          console.log('✅ Gallery modal event listeners initialized');
        }
      } catch (error) {
        console.warn('Gallery modal initialization failed:', error);
      }
    });
  </script>
</body>
</html>
