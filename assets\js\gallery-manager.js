/**
 * Gallery Management System for GetTwisted Studios Booking Interface
 * Optimized image loading and display for futuristic design
 * @version 1.0.0
 */

class GalleryManager {
    constructor() {
        this.galleryData = null;
        this.galleryImages = {};

        this.lazyLoadObserver = null;
        this.loadedImages = new Set();
        this.performanceMode = this.detectPerformanceMode();
        this.init();
    }

    detectPerformanceMode() {
        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
        const isLowEnd = connection && (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g');
        const isReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        const isMobile = window.innerWidth <= 768;

        return {
            isLowEnd,
            isReducedMotion,
            isMobile,
            enableLazyLoading: true,
            maxConcurrentLoads: isLowEnd ? 2 : 4
        };
    }

    async init() {
        await this.loadGalleryData();
        this.setupLazyLoading();
        this.preloadCriticalImages();
        console.log('🖼️ Gallery Manager initialized');
    }

    async loadGalleryData() {
        try {
            console.log('📡 Loading gallery data from assets/data/gallery-images.json...');
            const response = await fetch('assets/data/gallery-images.json');

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            this.galleryData = await response.json();

            // Convert to the format expected by existing methods
            Object.keys(this.galleryData.services).forEach(serviceId => {
                this.galleryImages[serviceId] = this.galleryData.services[serviceId].images;
            });

            console.log(`✅ Gallery data loaded successfully with ${Object.keys(this.galleryData.services).length} services`);
        } catch (error) {
            console.error('❌ Failed to load gallery data:', error);
            console.error('Error details:', error.message);
            console.warn('🔄 Using fallback gallery data...');
            this.loadFallbackImages();
        }
    }

    loadFallbackImages() {
        // Fallback image data in case JSON fails to load
        this.galleryImages = {
            'dreadlocks_retwist': [
                { src: 'assets/images/gallery/sm_retwist.jpg', alt: 'Professional dreadlock retwist service', category: 'locs', featured: true },
                { src: 'assets/images/gallery/rs1.jpg', alt: 'Loc retwist transformation', category: 'locs', featured: false }
            ],
            'two_strand_twists': [
                { src: 'assets/images/gallery/sm_twists.jpg', alt: 'Two strand twist protective style', category: 'braids', featured: true },
                { src: 'assets/images/gallery/rs2.jpg', alt: 'Beautiful two strand twists', category: 'braids', featured: false }
            ],
            'sew_in_weave': [
                { src: 'assets/images/gallery/rs4.jpg', alt: 'Professional sew-in weave installation', category: 'braids', featured: true },
                { src: 'assets/images/gallery/rs6.jpg', alt: 'Sew-in weave styling', category: 'braids', featured: false }
            ],
            'bantu_knots': [
                { src: 'assets/images/gallery/sm_corn_rows.jpg', alt: 'Bantu knots natural styling', category: 'braids', featured: true },
                { src: 'assets/images/gallery/rs9.jpg', alt: 'Bantu knot protective style', category: 'braids', featured: false }
            ],
            'take_out_weave': [
                { src: 'assets/images/gallery/rs3.jpg', alt: 'Professional weave removal service', category: 'braids', featured: true },
                { src: 'assets/images/gallery/rs5.jpg', alt: 'Hair care after weave removal', category: 'braids', featured: false }
            ],
            'kinky_twists': [
                { src: 'assets/images/gallery/sm_twists.jpg', alt: 'Kinky twist protective styling', category: 'braids', featured: true },
                { src: 'assets/images/gallery/rs7.jpg', alt: 'Long kinky twists', category: 'braids', featured: false }
            ],
            'starter_locs': [
                { src: 'assets/images/gallery/rs1.jpg', alt: 'Starter locs installation', category: 'locs', featured: true },
                { src: 'assets/images/gallery/rs3.jpg', alt: 'Fresh starter locs', category: 'locs', featured: false }
            ],
            'haircuts': [
                { src: 'assets/images/gallery/rs1.jpg', alt: 'Professional haircut service', category: 'barber', featured: true },
                { src: 'assets/images/gallery/rs2.jpg', alt: 'Precision barbering', category: 'barber', featured: false }
            ],
            'beard_trim': [
                { src: 'assets/images/gallery/rs4.jpg', alt: 'Professional beard trimming', category: 'barber', featured: true },
                { src: 'assets/images/gallery/rs5.jpg', alt: 'Beard shaping and styling', category: 'barber', featured: false }
            ]
        };

        // Create fallback galleryData structure
        this.galleryData = {
            services: {},
            featured_gallery: [
                {
                    src: 'assets/images/gallery/sm_retwist.jpg',
                    alt: 'Professional dreadlock retwist service',
                    service: 'dreadlocks_retwist',
                    category: 'locs'
                },
                {
                    src: 'assets/images/gallery/sm_twists.jpg',
                    alt: 'Two strand twist protective style',
                    service: 'two_strand_twists',
                    category: 'braids'
                },
                {
                    src: 'assets/images/gallery/rs4.jpg',
                    alt: 'Professional sew-in weave installation',
                    service: 'sew_in_weave',
                    category: 'braids'
                },
                {
                    src: 'assets/images/gallery/sm_corn_rows.jpg',
                    alt: 'Bantu knots natural styling',
                    service: 'bantu_knots',
                    category: 'braids'
                }
            ]
        };

        // Convert to services format
        Object.keys(this.galleryImages).forEach(serviceId => {
            this.galleryData.services[serviceId] = {
                category: this.galleryImages[serviceId][0].category,
                name: serviceId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                images: this.galleryImages[serviceId]
            };
        });

        console.log('✅ Fallback gallery data loaded');
    }

    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            this.lazyLoadObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        this.lazyLoadObserver.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.1
            });
        }
    }

    preloadCriticalImages() {
        // Preload first image from each main service category
        const criticalServices = ['dreadlocks_retwist', 'two_strand_twists', 'sew_in_weave'];
        criticalServices.forEach(serviceId => {
            const images = this.galleryImages[serviceId];
            if (images && images.length > 0) {
                this.preloadImage(images[0].src);
            }
        });
    }

    preloadImage(src) {
        if (!this.loadedImages.has(src)) {
            const img = new Image();
            img.onload = () => {
                this.loadedImages.add(src);
                console.log(`✅ Preloaded: ${src}`);
            };
            img.onerror = () => {
                console.warn(`⚠️ Failed to preload: ${src}`);
            };
            img.src = src;
        }
    }

    loadImage(imgElement) {
        const src = imgElement.dataset.src;
        if (src && !this.loadedImages.has(src)) {
            imgElement.src = src;
            imgElement.classList.add('loading');
            
            imgElement.onload = () => {
                imgElement.classList.remove('loading');
                imgElement.classList.add('loaded');
                this.loadedImages.add(src);
                this.addImageEffects(imgElement);
            };
            
            imgElement.onerror = () => {
                imgElement.classList.remove('loading');
                imgElement.classList.add('error');
                console.warn(`⚠️ Failed to load image: ${src}`);
            };
        }
    }

    addImageEffects(imgElement) {
        if (!this.performanceMode.isReducedMotion) {
            imgElement.style.animation = 'fadeInGlow 0.6s ease-out';
        }
    }

    getServiceImages(serviceId, limit = 4) {
        const images = this.galleryImages[serviceId] || [];
        return images.slice(0, limit);
    }

    createGalleryHTML(serviceId, options = {}) {
        const {
            limit = 4,
            showThumbnails = true,
            enableModal = true,
            className = 'service-gallery'
        } = options;

        const images = this.getServiceImages(serviceId, limit);
        if (images.length === 0) return '';

        let html = `<div class="${className}" data-service="${serviceId}">`;
        
        if (showThumbnails) {
            html += '<div class="gallery-thumbnails grid grid-cols-2 gap-2 mb-4">';
            images.forEach((image, index) => {
                html += `
                    <div class="gallery-thumbnail relative overflow-hidden rounded-lg group cursor-pointer"
                         ${enableModal ? `onclick="galleryManager.openStaticModal('${serviceId}', ${index})"` : ''}>
                        <img data-src="${image.src}"
                             alt="${image.alt}"
                             class="gallery-image w-full h-24 object-cover transition-transform duration-300 group-hover:scale-110 lazy-load"
                             loading="lazy">
                        <div class="gallery-overlay absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-2 left-2 text-white text-xs font-medium">
                                <i class="fas fa-search-plus mr-1"></i>View
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
        }

        html += '</div>';
        return html;
    }

    createServiceShowcase(serviceId, containerElement) {
        const images = this.getServiceImages(serviceId, 1);
        if (images.length === 0) return;

        const showcaseHTML = `
            <div class="service-showcase relative mb-4 rounded-lg overflow-hidden">
                <img data-src="${images[0].src}" 
                     alt="${images[0].alt}"
                     class="showcase-image w-full h-32 object-cover lazy-load"
                     loading="lazy">
                <div class="showcase-overlay absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent">
                    <div class="absolute bottom-2 left-2 text-white">
                        <div class="text-xs opacity-80">View Gallery</div>
                    </div>
                </div>
            </div>
        `;

        containerElement.insertAdjacentHTML('afterbegin', showcaseHTML);
        this.observeLazyImages(containerElement);
    }

    observeLazyImages(container = document) {
        if (this.lazyLoadObserver) {
            const lazyImages = container.querySelectorAll('.lazy-load:not([src])');
            lazyImages.forEach(img => {
                this.lazyLoadObserver.observe(img);
            });
        } else {
            // Fallback for browsers without IntersectionObserver
            const lazyImages = container.querySelectorAll('.lazy-load:not([src])');
            lazyImages.forEach(img => this.loadImage(img));
        }
    }

    // Use static modal instead of creating dynamic ones
    openStaticModal(serviceId, imageIndex = 0) {
        const images = this.galleryImages[serviceId];
        if (!images || !images[imageIndex]) return;

        const image = images[imageIndex];
        const serviceData = this.getServiceData(serviceId);

        // Prepare data for static modal
        const modalData = {
            src: image.src,
            alt: image.alt || '',
            title: serviceData?.name || image.title || 'Gallery Image',
            description: serviceData?.description || image.description || '',
            category: serviceData?.category || '',
            stylist: serviceData?.stylist || ''
        };

        // Call the global static modal function
        if (typeof window.openGalleryModal === 'function') {
            window.openGalleryModal(modalData);
        } else {
            console.warn('Static gallery modal function not available');
            // Fallback to old system if needed
            this.openModal(serviceId, imageIndex);
        }
    }

    // Keep old method for backward compatibility but mark as deprecated
    openModal(serviceId, imageIndex = 0) {
        console.warn('openModal is deprecated, use openStaticModal instead');
        const images = this.galleryImages[serviceId];
        if (!images || !images[imageIndex]) return;

        const modal = this.createModal(images, imageIndex, serviceId);
        document.body.appendChild(modal);

        // Trigger modal animation
        setTimeout(() => {
            modal.classList.add('active');
        }, 10);
    }

    // Helper method to get service data
    getServiceData(serviceId) {
        // This would ideally come from a services data structure
        // For now, return basic info based on serviceId
        const serviceMap = {
            'locs': { name: 'Locs Services', category: 'Locs', description: 'Professional loc maintenance and styling' },
            'braids': { name: 'Braiding Services', category: 'Braids', description: 'Creative braiding and protective styles' },
            'barber': { name: 'Barber Services', category: 'Barber', description: 'Professional cuts and grooming' }
        };

        return serviceMap[serviceId] || { name: 'Hair Services', category: 'General', description: 'Professional hair services' };
    }

    createModal(images, currentIndex, serviceId) {
        const modal = document.createElement('div');
        modal.className = 'gallery-modal fixed inset-0 z-50 bg-black/90 backdrop-blur-sm flex items-center justify-center p-4';
        modal.innerHTML = `
            <div class="modal-content glass-enhanced rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
                <div class="modal-header flex justify-between items-center p-4 border-b border-white/20">
                    <h3 class="text-xl font-bold text-neon-cyan">Service Gallery</h3>
                    <button class="close-modal text-white hover:text-neon-cyan transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div class="modal-body p-4">
                    <div class="main-image mb-4">
                        <img src="${images[currentIndex].src}" 
                             alt="${images[currentIndex].alt}"
                             class="w-full h-96 object-cover rounded-lg">
                    </div>
                    <div class="image-thumbnails flex gap-2 overflow-x-auto">
                        ${images.map((img, index) => `
                            <img src="${img.src}" 
                                 alt="${img.alt}"
                                 class="thumbnail w-16 h-16 object-cover rounded cursor-pointer ${index === currentIndex ? 'ring-2 ring-neon-cyan' : ''}"
                                 onclick="galleryManager.switchModalImage(${index})">
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        // Add event listeners
        modal.querySelector('.close-modal').addEventListener('click', () => this.closeModal(modal));
        modal.addEventListener('click', (e) => {
            if (e.target === modal) this.closeModal(modal);
        });

        return modal;
    }

    closeModal(modal) {
        modal.classList.remove('active');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }

    switchModalImage(index) {
        const mainImage = document.querySelector('.gallery-modal .main-image img');
        const thumbnails = document.querySelectorAll('.gallery-modal .thumbnail');
        
        if (mainImage && thumbnails[index]) {
            mainImage.src = thumbnails[index].src;
            mainImage.alt = thumbnails[index].alt;
            
            thumbnails.forEach(thumb => thumb.classList.remove('ring-2', 'ring-neon-cyan'));
            thumbnails[index].classList.add('ring-2', 'ring-neon-cyan');
        }
    }

    // Initialize gallery for booking interface
    initBookingGallery() {
        const serviceCards = document.querySelectorAll('.service-card');
        serviceCards.forEach(card => {
            const serviceButton = card.querySelector('[data-service]');
            if (serviceButton) {
                const serviceId = serviceButton.getAttribute('data-service');
                this.createServiceShowcase(serviceId, card);
            }
        });
    }
}

// Initialize gallery manager immediately and on DOM ready
if (typeof window.galleryManager === 'undefined') {
    window.galleryManager = new GalleryManager();
    console.log('🖼️ Gallery Manager created and available globally');
}

// Ensure it's also available when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (typeof window.galleryManager === 'undefined') {
        window.galleryManager = new GalleryManager();
        console.log('🖼️ Gallery Manager ready (DOM loaded)');
    } else {
        console.log('🖼️ Gallery Manager already available');
    }
});
