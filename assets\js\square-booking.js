/**
 * Square Booking Integration for GetTwisted Studios
 * Production-ready booking system with performance optimizations and security
 * @version 2.0.0
 */

class SquareBookingManager {
    constructor() {
        this.baseBookingUrl = 'https://book.squareup.com/appointments/dp9qk14alq4461/location/LQDVMV8ZMNWDD';
        this.businessData = {
            name: 'Get Twisted Hair Studio',
            phone: '(*************',
            email: '<EMAIL>',
            address: 'Teaneck, NJ | Pottstown, PA'
        };
        this.services = {};
        this.isLoading = false;
        this.retryCount = 0;
        this.maxRetries = 3;
        this.performanceMode = this.detectPerformanceMode();
        this.analytics = {
            bookingAttempts: 0,
            successfulBookings: 0,
            errors: []
        };

        // Service mapping for Square Appointments
        // Note: Square Appointments uses the base URL and service selection happens within the booking flow
        this.serviceMapping = {
            'dreadlocks_retwist': {
                name: 'Dreadlocks Retwist',
                squareServiceName: 'Loc Retwist',
                category: 'Loc Services',
                price: '$120',
                duration: '1hr 30min'
            },
            'two_strand_twists': {
                name: 'Two Strand Twists',
                squareServiceName: 'Two Strand Twists',
                category: 'Twist Services',
                price: '$150',
                duration: '2hr'
            },
            'sew_in_weave': {
                name: 'Sew in Weave',
                squareServiceName: 'Sew in Weave',
                category: 'Braiding Services',
                price: '$250',
                duration: '3hr'
            },
            'bantu_knots': {
                name: 'Bantu Knots',
                squareServiceName: 'Bantu Knots',
                category: 'Braiding Services',
                price: '$120',
                duration: '1hr'
            },
            'take_out_weave': {
                name: 'Take out weave',
                squareServiceName: 'Take out weave',
                category: 'Braiding Services',
                price: '$75',
                duration: '4hr'
            },
            'kinky_twists': {
                name: 'Kinky Twists',
                squareServiceName: 'Kinky Twists',
                category: 'Braiding Services',
                price: '$250',
                duration: '4hr'
            },
            'starter_locs': {
                name: 'Starter Locs',
                squareServiceName: 'Starter Locs',
                category: 'Loc Services',
                price: '$160',
                duration: '2-4hr'
            },
            'haircuts': {
                name: 'Men\'s Haircut',
                squareServiceName: 'Men\'s Haircut',
                category: 'Barber Services',
                price: '$45',
                duration: '30-45min'
            },
            'beard_trim': {
                name: 'Beard Trim',
                squareServiceName: 'Beard Trim',
                category: 'Barber Services',
                price: '$20',
                duration: '15-20min'
            },
            // Additional Loc Services
            'loc_detox': {
                name: 'Loc Detox',
                squareServiceName: 'Loc Detox',
                category: 'Loc Services',
                price: '$50',
                duration: '1-1.5hr'
            },
            'loc_styling': {
                name: 'Loc Styling',
                squareServiceName: 'Loc Styling',
                category: 'Loc Services',
                price: 'Starting at $60',
                duration: '1-2hr'
            },
            'loc_coloring': {
                name: 'Loc Coloring',
                squareServiceName: 'Loc Coloring',
                category: 'Loc Services',
                price: '$50 and up',
                duration: '2-4hr'
            },
            'loc_repair': {
                name: 'Loc Repair',
                squareServiceName: 'Loc Repair',
                category: 'Loc Services',
                price: '$15-$25 per loc',
                duration: 'Varies'
            },
            'loc_extensions': {
                name: 'Loc Extensions',
                squareServiceName: 'Loc Extensions',
                category: 'Loc Services',
                price: '$250-$500',
                duration: '4-8hr'
            },
            // Braiding Services
            'knotless_braids_shoulder': {
                name: 'Knotless Braids - Shoulder Length',
                squareServiceName: 'Knotless Braids (Shoulder)',
                category: 'Braiding Services',
                price: '$200',
                duration: '4-5hr'
            },
            'knotless_braids_midback': {
                name: 'Knotless Braids - Mid-Back Length',
                squareServiceName: 'Knotless Braids (Mid-Back)',
                category: 'Braiding Services',
                price: '$250',
                duration: '5-7hr'
            },
            'knotless_braids_waist': {
                name: 'Knotless Braids - Waist Length',
                squareServiceName: 'Knotless Braids (Waist)',
                category: 'Braiding Services',
                price: '$300',
                duration: '6-8hr'
            },
            'cornrows': {
                name: 'Cornrows',
                squareServiceName: 'Cornrows',
                category: 'Braiding Services',
                price: 'Starting at $80',
                duration: '2-4hr'
            },
            'french_braids': {
                name: 'French Braids',
                squareServiceName: 'French Braids',
                category: 'Braiding Services',
                price: 'Starting at $60',
                duration: '1-2hr'
            },
            // Barber Services
            'mens_haircut': {
                name: 'Men\'s Haircut',
                squareServiceName: 'Men\'s Haircut',
                category: 'Barber Services',
                price: '$45',
                duration: '30-45min'
            },
            'fade_cut': {
                name: 'Fade Cut',
                squareServiceName: 'Fade Cut',
                category: 'Barber Services',
                price: '$50',
                duration: '45min'
            },
            'beard_styling': {
                name: 'Beard Styling',
                squareServiceName: 'Beard Styling',
                category: 'Barber Services',
                price: '$30',
                duration: '30min'
            }
        };

        // Gallery image mappings for service showcase
        this.serviceGallery = {
            'dreadlocks_retwist': [
                'assets/images/gallery/sm_retwist.jpg',
                'assets/images/gallery/sm_retwist_01.jpg',
                'assets/images/gallery/rs1.jpg',
                'assets/images/gallery/rs3.jpg'
            ],
            'two_strand_twists': [
                'assets/images/gallery/sm_twists.jpg',
                'assets/images/gallery/rs2.jpg',
                'assets/images/gallery/rs5.jpg',
                'assets/images/gallery/rs7.jpg'
            ],
            'sew_in_weave': [
                'assets/images/gallery/rs4.jpg',
                'assets/images/gallery/rs6.jpg',
                'assets/images/gallery/rs8.jpg',
                'assets/images/gallery/rs10.jpg'
            ],
            'bantu_knots': [
                'assets/images/gallery/sm_corn_rows.jpg',
                'assets/images/gallery/rs9.jpg',
                'assets/images/gallery/rs1.jpg',
                'assets/images/gallery/rs2.jpg'
            ],
            'take_out_weave': [
                'assets/images/gallery/rs3.jpg',
                'assets/images/gallery/rs4.jpg',
                'assets/images/gallery/rs5.jpg',
                'assets/images/gallery/rs6.jpg'
            ],
            'kinky_twists': [
                'assets/images/gallery/sm_twists.jpg',
                'assets/images/gallery/rs7.jpg',
                'assets/images/gallery/rs8.jpg',
                'assets/images/gallery/rs9.jpg'
            ]
        };

        this.init();
    }

    detectPerformanceMode() {
        // Detect device performance capabilities
        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
        const isLowEnd = connection && (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g');
        const isReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        const isMobile = window.innerWidth <= 768;

        return {
            isLowEnd,
            isReducedMotion,
            isMobile,
            enableAnimations: !isLowEnd && !isReducedMotion
        };
    }

    async init() {
        try {
            this.showLoadingState();
            await this.loadServices();
            this.initBookingButtons();
            this.initServiceCards();
            this.createBookingModal();
            this.setupErrorHandling();
            this.setupAnalytics();
            this.hideLoadingState();
            this.logAnalytics('initialization', 'success');
        } catch (error) {
            this.handleError('Initialization failed', error);
            this.hideLoadingState();
        }
    }

    showLoadingState() {
        const existingLoader = document.getElementById('booking-loader');
        if (!existingLoader) {
            const loader = document.createElement('div');
            loader.id = 'booking-loader';
            loader.className = 'fixed top-4 right-4 z-50 bg-gray-900 text-white p-3 rounded-lg shadow-lg';
            loader.innerHTML = `
                <div class="flex items-center">
                    <div class="loading-cyber mr-2"></div>
                    <span class="text-sm">Loading booking system...</span>
                </div>
            `;
            document.body.appendChild(loader);
        }
    }

    hideLoadingState() {
        const loader = document.getElementById('booking-loader');
        if (loader) {
            loader.remove();
        }
    }

    setupErrorHandling() {
        window.addEventListener('error', (event) => {
            if (event.filename && event.filename.includes('square')) {
                this.handleError('Square booking script error', event.error);
            }
        });

        window.addEventListener('unhandledrejection', (event) => {
            if (event.reason && event.reason.toString().includes('square')) {
                this.handleError('Square booking promise rejection', event.reason);
            }
        });
    }

    setupAnalytics() {
        // Track page visibility for booking analytics
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.logAnalytics('page_visibility', 'hidden');
            } else {
                this.logAnalytics('page_visibility', 'visible');
            }
        });
    }

    logAnalytics(action, status, details = {}) {
        const analyticsData = {
            timestamp: new Date().toISOString(),
            action,
            status,
            details,
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        // Store in session storage for analytics
        const existingData = JSON.parse(sessionStorage.getItem('booking_analytics') || '[]');
        existingData.push(analyticsData);
        sessionStorage.setItem('booking_analytics', JSON.stringify(existingData.slice(-50))); // Keep last 50 events

        // Console log for development
        if (window.location.hostname === 'localhost' || window.location.hostname.includes('127.0.0.1')) {
            console.log('Booking Analytics:', analyticsData);
        }
    }

    async loadServices() {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

            const response = await fetch('/assets/data/services-pricing.json', {
                signal: controller.signal,
                headers: {
                    'Accept': 'application/json',
                    'Cache-Control': 'max-age=300' // 5 minute cache
                }
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            // Validate data structure
            if (!data.categories || !Array.isArray(data.categories)) {
                throw new Error('Invalid services data structure');
            }

            // Create a flat map of services with Square service IDs
            data.categories.forEach(category => {
                if (category.services && Array.isArray(category.services)) {
                    category.services.forEach(service => {
                        if (service.square_service_id && this.isValidServiceData(service)) {
                            this.services[service.square_service_id] = {
                                ...service,
                                category: category.name,
                                sanitizedName: this.sanitizeString(service.name),
                                sanitizedDescription: this.sanitizeString(service.description)
                            };
                        }
                    });
                }
            });

            this.logAnalytics('services_loaded', 'success', { count: Object.keys(this.services).length });
        } catch (error) {
            this.handleError('Failed to load services', error);
            this.loadFallbackServices();
        }
    }

    isValidServiceData(service) {
        return service.name &&
               service.description &&
               service.price_range &&
               service.time_range &&
               typeof service.name === 'string' &&
               typeof service.description === 'string';
    }

    sanitizeString(str) {
        if (typeof str !== 'string') return '';
        return str.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                  .replace(/[<>]/g, '')
                  .trim();
    }

    loadFallbackServices() {
        // Use the service mapping as fallback data
        this.services = {};

        Object.keys(this.serviceMapping).forEach(serviceId => {
            const service = this.serviceMapping[serviceId];
            this.services[serviceId] = {
                name: service.name,
                description: `Professional ${service.category.toLowerCase()} service`,
                price_range: service.price,
                time_range: service.duration,
                category: service.category,
                squareServiceName: service.squareServiceName
            };
        });

        this.logAnalytics('services_loaded', 'fallback');
        console.log('✅ Loaded fallback services from service mapping:', Object.keys(this.services));
    }

    handleError(message, error) {
        const errorData = {
            message,
            error: error?.message || error,
            stack: error?.stack,
            timestamp: new Date().toISOString()
        };

        this.analytics.errors.push(errorData);
        this.logAnalytics('error', 'occurred', errorData);

        // Silent fallback - no error messages shown to user
        console.warn('Square Booking Error (silent fallback):', errorData);

        // Don't automatically open booking on errors - let user initiate
    }

    // Error message function removed - using silent fallback instead

    initBookingButtons() {
        // Add booking buttons to existing service cards
        const serviceCards = document.querySelectorAll('.service-card, .bg-white');
        
        serviceCards.forEach(card => {
            const existingButton = card.querySelector('.book-appointment-btn');
            if (!existingButton) {
                this.addBookingButtonToCard(card);
            }
        });
    }

    addBookingButtonToCard(card) {
        const serviceName = this.extractServiceName(card);
        const serviceId = this.findServiceId(serviceName);
        
        if (serviceId) {
            const bookingButton = this.createBookingButton(serviceId, serviceName);
            
            // Find the best place to insert the button
            const buttonContainer = card.querySelector('.mt-4, .flex, .space-y-2');
            if (buttonContainer) {
                buttonContainer.appendChild(bookingButton);
            } else {
                card.appendChild(bookingButton);
            }
        }
    }

    extractServiceName(card) {
        const heading = card.querySelector('h3, h4, .font-bold');
        return heading ? heading.textContent.trim() : '';
    }

    findServiceId(serviceName) {
        // Map service names to Square service IDs
        const serviceMap = {
            'Dreadlocks Retwist': 'dreadlocks_retwist',
            'Retwist & Maintenance': 'dreadlocks_retwist',
            'Two Strand Twists': 'two_strand_twists',
            'Sew in Weave': 'sew_in_weave',
            'Bantu Knots': 'bantu_knots',
            'Take out weave': 'take_out_weave',
            'Kinky Twists': 'kinky_twists'
        };

        // Try exact match first
        if (serviceMap[serviceName]) {
            return serviceMap[serviceName];
        }

        // Try partial match
        for (const [name, id] of Object.entries(serviceMap)) {
            if (serviceName.toLowerCase().includes(name.toLowerCase()) || 
                name.toLowerCase().includes(serviceName.toLowerCase())) {
                return id;
            }
        }

        return null;
    }

    createBookingButton(serviceId, serviceName) {
        const button = document.createElement('button');
        button.className = 'book-appointment-btn btn-holographic w-full mt-4 px-6 py-3 rounded-lg font-bold text-white transition-all duration-300 hover:scale-105 pulse-neon';
        button.innerHTML = `
            <i class="fas fa-calendar-plus mr-2"></i>
            Book Now
        `;
        
        button.addEventListener('click', (e) => {
            e.preventDefault();
            this.openBookingFlow(serviceId, serviceName);
        });

        return button;
    }

    openBookingFlow(serviceId, serviceName) {
        try {
            this.analytics.bookingAttempts++;
            this.logAnalytics('booking_attempt', 'started', { serviceId, serviceName });

            // Use enhanced booking with service guidance
            if (serviceId && this.serviceMapping[serviceId]) {
                this.openBookingWithGuidance(serviceId, serviceName);
            } else {
                // Fallback for unknown services
                this.openDirectBookingLink(serviceId, serviceName);
            }
        } catch (error) {
            this.handleError('Failed to open booking flow', error);
        }
    }

    openDirectBookingLink(serviceId, serviceName) {
        const bookingUrl = this.generateBookingUrl(serviceId);

        // Add service context for better user experience
        const service = this.serviceMapping[serviceId];
        const windowTitle = service ? `Book ${service.name} - GetTwisted Studios` : 'Book Appointment - GetTwisted Studios';

        const newWindow = window.open(
            bookingUrl,
            '_blank',
            'width=900,height=700,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no'
        );

        if (!newWindow) {
            this.handleError('Popup blocked - please allow popups for booking');
            // Fallback to same window
            window.location.href = bookingUrl;
        } else {
            // Set window title if possible
            try {
                newWindow.document.title = windowTitle;
            } catch (e) {
                // Cross-origin restriction, ignore
            }
            this.logAnalytics('booking_opened', 'direct_link', { serviceId, serviceName, bookingUrl });
        }
    }

    showBookingModal(serviceId, serviceName) {
        const service = this.services[serviceId];
        const modal = document.getElementById('square-booking-modal');

        if (!modal) {
            this.handleError('Booking modal not found');
            this.openDirectBookingLink(serviceId, serviceName);
            return;
        }

        try {
            // Update modal content with sanitized data
            const modalTitle = modal.querySelector('.modal-title');
            const modalService = modal.querySelector('.modal-service');
            const modalPrice = modal.querySelector('.modal-price');
            const modalDuration = modal.querySelector('.modal-duration');
            const bookingFrame = modal.querySelector('#booking-iframe');

            if (modalTitle) modalTitle.textContent = 'Book Your Appointment';
            if (modalService) modalService.textContent = this.sanitizeString(serviceName);
            if (modalPrice && service) modalPrice.textContent = this.sanitizeString(service.price_range);
            if (modalDuration && service) modalDuration.textContent = this.sanitizeString(service.time_range);

            // Show modal with loading state
            modal.classList.remove('hidden');
            modal.classList.add('flex');

            // Add ARIA attributes for accessibility
            modal.setAttribute('aria-modal', 'true');
            modal.setAttribute('aria-labelledby', 'booking-modal-title');
            modal.setAttribute('role', 'dialog');

            // Focus management for accessibility
            const firstFocusable = modal.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
            if (firstFocusable) {
                firstFocusable.focus();
            }

            // Lazy load the iframe for better performance
            this.lazyLoadBookingFrame(bookingFrame, serviceId);

            // Add animation if performance allows
            if (this.performanceMode.enableAnimations) {
                setTimeout(() => {
                    modal.classList.add('modal-open');
                }, 10);
            }

            this.logAnalytics('booking_modal', 'opened', { serviceId, serviceName });
        } catch (error) {
            this.handleError('Failed to show booking modal', error);
            this.openDirectBookingLink(serviceId, serviceName);
        }
    }

    lazyLoadBookingFrame(iframe, serviceId) {
        if (!iframe) return;

        const loadingOverlay = iframe.parentNode?.querySelector('.iframe-loading');

        // Set up intersection observer for lazy loading
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const bookingUrl = this.generateBookingUrl(serviceId);
                    iframe.src = bookingUrl;

                    // Set up iframe load handlers
                    iframe.onload = () => {
                        if (loadingOverlay) {
                            loadingOverlay.style.display = 'none';
                        }
                        this.logAnalytics('booking_iframe', 'loaded', { serviceId });
                    };

                    iframe.onerror = () => {
                        this.handleError('Failed to load booking system');
                        if (loadingOverlay) {
                            loadingOverlay.innerHTML = `
                                <div class="text-center">
                                    <i class="fas fa-exclamation-triangle text-red-400 text-2xl mb-2"></i>
                                    <p class="text-red-300 mb-4">Unable to load booking system</p>
                                    <button onclick="window.open('${bookingUrl}', '_blank')"
                                            class="btn-holographic px-4 py-2 rounded">
                                        Open in New Window
                                    </button>
                                </div>
                            `;
                        }
                    };

                    observer.unobserve(iframe);
                }
            });
        }, { threshold: 0.1 });

        observer.observe(iframe);
    }

    generateBookingUrl(serviceId) {
        // Enhanced booking URL generation with service context
        const url = new URL(this.baseBookingUrl);

        // Add UTM parameters for tracking booking source
        url.searchParams.set('utm_source', 'website');
        url.searchParams.set('utm_medium', 'booking_button');
        url.searchParams.set('utm_campaign', 'service_booking');

        // Add service information for analytics and user context
        if (serviceId && this.serviceMapping[serviceId]) {
            const service = this.serviceMapping[serviceId];
            url.searchParams.set('utm_content', serviceId);
            url.searchParams.set('service_category', service.category.toLowerCase().replace(/\s+/g, '_'));

            // Add service context to URL fragment for client-side handling
            url.hash = `service=${encodeURIComponent(service.squareServiceName)}`;
        }

        return url.toString();
    }

    /**
     * Enhanced booking flow with service pre-selection guidance
     * Since Square doesn't support direct service pre-selection via URL,
     * we provide user guidance and context
     */
    openBookingWithGuidance(serviceId, serviceName) {
        const service = this.serviceMapping[serviceId];

        if (service) {
            // Show service selection guidance modal first
            this.showServiceGuidanceModal(service, () => {
                this.openDirectBookingLink(serviceId, serviceName);
            });
        } else {
            // Fallback to direct booking
            this.openDirectBookingLink(serviceId, serviceName);
        }
    }

    /**
     * Show guidance modal to help users select the correct service
     */
    showServiceGuidanceModal(service, onProceed) {
        const modal = this.createServiceGuidanceModal(service, onProceed);
        document.body.appendChild(modal);

        // Show modal with animation
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
    }

    /**
     * Create service guidance modal
     */
    createServiceGuidanceModal(service, onProceed) {
        const modal = document.createElement('div');
        modal.className = 'service-guidance-modal';
        modal.innerHTML = `
            <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
            <div class="modal-content glassmorphism">
                <div class="modal-header">
                    <h3 class="text-xl font-bold text-neon-cyan mb-2">
                        <i class="fas fa-info-circle mr-2"></i>Service Selection Guide
                    </h3>
                    <button class="modal-close" onclick="this.closest('.service-guidance-modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="modal-body">
                    <div class="service-info mb-4">
                        <h4 class="text-lg font-semibold text-neon-purple mb-2">${service.name}</h4>
                        <div class="service-details grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <span class="text-sm text-gray-400">Price:</span>
                                <div class="text-white font-semibold">${service.price}</div>
                            </div>
                            <div>
                                <span class="text-sm text-gray-400">Duration:</span>
                                <div class="text-white font-semibold">${service.duration}</div>
                            </div>
                        </div>
                    </div>

                    <div class="guidance-text mb-6">
                        <p class="text-gray-300 mb-3">
                            <i class="fas fa-lightbulb text-yellow-400 mr-2"></i>
                            When the booking page opens, please look for "<strong class="text-neon-cyan">${service.squareServiceName}</strong>"
                            in the <strong>${service.category}</strong> section.
                        </p>
                        <div class="tips bg-black/30 p-3 rounded-lg">
                            <p class="text-sm text-gray-400 mb-2">
                                <i class="fas fa-star text-yellow-400 mr-1"></i>
                                Tip: Use the search or filter options to quickly find your service
                            </p>
                            <p class="text-sm text-gray-400">
                                <i class="fas fa-phone text-green-400 mr-1"></i>
                                Need help? Call us at ${this.businessData.phone}
                            </p>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button onclick="this.closest('.service-guidance-modal').remove()"
                            class="btn-secondary px-4 py-2 rounded-lg mr-3">
                        Cancel
                    </button>
                    <button onclick="this.closest('.service-guidance-modal').remove(); (${onProceed.toString()})()"
                            class="btn-holographic px-6 py-3 rounded-lg">
                        <i class="fas fa-external-link-alt mr-2"></i>Continue to Booking
                    </button>
                </div>
            </div>
        `;

        return modal;
    }

    createBookingModal() {
        // Check if modal already exists
        if (document.getElementById('square-booking-modal')) {
            return;
        }

        const modal = document.createElement('div');
        modal.id = 'square-booking-modal';
        modal.className = 'fixed inset-0 bg-black bg-opacity-75 hidden items-center justify-center z-50 backdrop-blur-sm booking-modal';
        modal.setAttribute('role', 'dialog');
        modal.setAttribute('aria-modal', 'true');
        modal.setAttribute('aria-labelledby', 'booking-modal-title');
        modal.setAttribute('aria-describedby', 'booking-modal-description');

        modal.innerHTML = `
            <div class="glass-enhanced bg-gray-900 rounded-2xl p-8 max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden relative" role="document">
                <!-- Header -->
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h2 id="booking-modal-title" class="modal-title text-2xl font-bold text-neon-cyan mb-2">Book Your Appointment</h2>
                        <div id="booking-modal-description" class="text-gray-300">
                            <p class="modal-service text-lg font-semibold" aria-live="polite"></p>
                            <div class="flex gap-4 mt-2 text-sm">
                                <span class="modal-price text-neon-green" aria-label="Service price"></span>
                                <span class="modal-duration text-neon-purple" aria-label="Service duration"></span>
                            </div>
                        </div>
                    </div>
                    <button class="close-modal text-gray-400 hover:text-white text-2xl transition-colors focus:outline-none focus:ring-2 focus:ring-neon-cyan rounded p-2"
                            aria-label="Close booking modal"
                            tabindex="0">
                        <i class="fas fa-times" aria-hidden="true"></i>
                    </button>
                </div>

                <!-- Progress Indicator -->
                <div class="booking-progress mb-4 hidden">
                    <div class="flex items-center justify-center space-x-2 text-sm text-gray-400">
                        <div class="progress-step active">
                            <i class="fas fa-calendar-alt mr-1"></i>
                            <span>Select Service</span>
                        </div>
                        <div class="progress-arrow">→</div>
                        <div class="progress-step">
                            <i class="fas fa-user mr-1"></i>
                            <span>Choose Stylist</span>
                        </div>
                        <div class="progress-arrow">→</div>
                        <div class="progress-step">
                            <i class="fas fa-clock mr-1"></i>
                            <span>Select Time</span>
                        </div>
                        <div class="progress-arrow">→</div>
                        <div class="progress-step">
                            <i class="fas fa-check mr-1"></i>
                            <span>Confirm</span>
                        </div>
                    </div>
                </div>

                <!-- Booking Frame -->
                <div class="booking-frame-container relative">
                    <iframe
                        id="booking-iframe"
                        src=""
                        class="w-full h-[600px] rounded-lg border border-gray-600"
                        frameborder="0"
                        sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox"
                        loading="lazy"
                        title="Square Appointments Booking System"
                        aria-label="Booking appointment interface">
                    </iframe>

                    <!-- Loading overlay -->
                    <div class="iframe-loading absolute inset-0 bg-gray-800 rounded-lg flex items-center justify-center">
                        <div class="text-center">
                            <div class="loading-cyber mx-auto mb-4" aria-hidden="true"></div>
                            <p class="text-gray-300" role="status" aria-live="polite">Loading secure booking system...</p>
                            <div class="mt-4 text-xs text-gray-500">
                                <i class="fas fa-shield-alt mr-1"></i>
                                Secured by Square
                            </div>
                        </div>
                    </div>

                    <!-- Error fallback -->
                    <div class="iframe-error absolute inset-0 bg-gray-800 rounded-lg flex items-center justify-center hidden">
                        <div class="text-center max-w-md">
                            <i class="fas fa-exclamation-triangle text-red-400 text-3xl mb-4"></i>
                            <h3 class="text-white font-bold mb-2">Booking System Unavailable</h3>
                            <p class="text-gray-300 mb-4 text-sm">We're having trouble loading the booking system. Please try one of these alternatives:</p>
                            <div class="space-y-2">
                                <button class="btn-holographic w-full py-2 px-4 rounded text-sm" onclick="location.reload()">
                                    <i class="fas fa-refresh mr-2"></i>Retry Loading
                                </button>
                                <a href="tel:${this.businessData.phone}" class="block bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded text-sm transition-colors">
                                    <i class="fas fa-phone mr-2"></i>Call ${this.businessData.phone}
                                </a>
                                <button class="open-external-booking bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded text-sm w-full transition-colors">
                                    <i class="fas fa-external-link-alt mr-2"></i>Open in New Window
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="mt-6 text-center text-sm text-gray-400">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-shield-alt text-green-400 mr-2"></i>
                        <span>Secured by Square Appointments</span>
                    </div>
                    <p class="flex items-center justify-center space-x-4">
                        <a href="tel:${this.businessData.phone}" class="hover:text-white transition-colors">
                            <i class="fas fa-phone mr-1"></i>${this.businessData.phone}
                        </a>
                        <span class="text-gray-600">|</span>
                        <a href="mailto:${this.businessData.email}" class="hover:text-white transition-colors">
                            <i class="fas fa-envelope mr-1"></i>${this.businessData.email}
                        </a>
                    </p>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        this.setupModalEventListeners(modal);
    }

    setupModalEventListeners(modal) {
        // Close button
        const closeButton = modal.querySelector('.close-modal');
        closeButton.addEventListener('click', () => this.closeBookingModal());

        // Click outside to close
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeBookingModal();
            }
        });

        // Keyboard navigation
        modal.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeBookingModal();
            } else if (e.key === 'Tab') {
                this.handleTabNavigation(e, modal);
            }
        });

        // External booking fallback
        const externalBookingBtn = modal.querySelector('.open-external-booking');
        if (externalBookingBtn) {
            externalBookingBtn.addEventListener('click', () => {
                window.open(this.baseBookingUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
                this.closeBookingModal();
            });
        }

        // Setup iframe error handling
        this.setupIframeErrorHandling(modal);
    }

    handleTabNavigation(e, modal) {
        const focusableElements = modal.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
        }
    }

    setupIframeErrorHandling(modal) {
        const iframe = modal.querySelector('#booking-iframe');
        const loadingOverlay = modal.querySelector('.iframe-loading');
        const errorOverlay = modal.querySelector('.iframe-error');

        let loadTimeout;

        iframe.addEventListener('load', () => {
            clearTimeout(loadTimeout);
            loadingOverlay.style.display = 'none';
            this.logAnalytics('booking_iframe', 'loaded_successfully');
        });

        iframe.addEventListener('error', () => {
            clearTimeout(loadTimeout);
            this.showIframeError(loadingOverlay, errorOverlay);
        });

        // Set a timeout for loading
        loadTimeout = setTimeout(() => {
            this.showIframeError(loadingOverlay, errorOverlay);
        }, 15000); // 15 second timeout
    }

    showIframeError(loadingOverlay, errorOverlay) {
        loadingOverlay.style.display = 'none';
        errorOverlay.classList.remove('hidden');
        this.handleError('Booking iframe failed to load');
    }

    closeBookingModal() {
        const modal = document.getElementById('square-booking-modal');
        if (modal) {
            modal.classList.remove('modal-open');
            setTimeout(() => {
                modal.classList.add('hidden');
                modal.classList.remove('flex');
                
                // Clear iframe src to stop loading
                const iframe = modal.querySelector('#booking-iframe');
                if (iframe) {
                    iframe.src = '';
                }
                
                // Show loading overlay for next time
                const loadingOverlay = modal.querySelector('.iframe-loading');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'flex';
                }
            }, 300);
        }
    }

    initServiceCards() {
        // Add enhanced service cards with booking integration
        this.createServiceShowcase();
    }

    createServiceShowcase() {
        // Find service sections and enhance them
        const serviceSections = document.querySelectorAll('.services-section, #services');
        
        serviceSections.forEach(section => {
            this.enhanceServiceSection(section);
        });
    }

    enhanceServiceSection(section) {
        // Add a prominent "Book Any Service" button
        const bookAllButton = document.createElement('div');
        bookAllButton.className = 'text-center mb-8';
        bookAllButton.innerHTML = `
            <button class="btn-holographic px-8 py-4 rounded-xl font-bold text-lg text-white transition-all duration-300 hover:scale-105 pulse-neon">
                <i class="fas fa-calendar-check mr-3"></i>
                Book Any Service Online
            </button>
            <p class="text-gray-400 mt-2 text-sm">Real-time availability • Instant confirmation</p>
        `;
        
        const button = bookAllButton.querySelector('button');
        button.addEventListener('click', () => {
            this.openGeneralBookingFlow();
        });
        
        // Insert at the beginning of the section
        section.insertBefore(bookAllButton, section.firstChild);
    }

    openGeneralBookingFlow() {
        // Open the general booking flow
        window.open(this.baseBookingUrl, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
    }

    // Utility method to create direct booking links
    createDirectBookingLink(serviceId, text = 'Book Now') {
        const link = document.createElement('a');
        link.href = this.generateBookingUrl(serviceId);
        link.target = '_blank';
        link.className = 'btn-holographic inline-block px-6 py-3 rounded-lg font-bold text-white transition-all duration-300 hover:scale-105';
        link.innerHTML = `<i class="fas fa-calendar-plus mr-2"></i>${text}`;
        return link;
    }

    // Method to add booking widget to any element
    addBookingWidget(element, serviceId = null) {
        if (serviceId) {
            const button = this.createBookingButton(serviceId, 'Book This Service');
            element.appendChild(button);
        } else {
            const button = this.createDirectBookingLink(null, 'Book Appointment');
            element.appendChild(button);
        }
    }
}

// CSS for modal animations
const style = document.createElement('style');
style.textContent = `
    .modal-open {
        animation: modalFadeIn 0.3s ease-out;
    }
    
    .modal-open > div {
        animation: modalSlideIn 0.3s ease-out;
    }
    
    @keyframes modalFadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    @keyframes modalSlideIn {
        from { 
            opacity: 0;
            transform: scale(0.9) translateY(-20px);
        }
        to { 
            opacity: 1;
            transform: scale(1) translateY(0);
        }
    }
    
    .book-appointment-btn:hover {
        box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
        transform: translateY(-2px);
    }

    /* Service Guidance Modal Styles */
    .service-guidance-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .service-guidance-modal.show {
        opacity: 1;
    }

    .service-guidance-modal .modal-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(5px);
    }

    .service-guidance-modal .modal-content {
        position: relative;
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        padding: 2rem;
        border-radius: 12px;
        border: 1px solid rgba(0, 255, 255, 0.3);
        transform: scale(0.9);
        transition: transform 0.3s ease;
    }

    .service-guidance-modal.show .modal-content {
        transform: scale(1);
    }

    .service-guidance-modal .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1.5rem;
    }

    .service-guidance-modal .modal-close {
        background: none;
        border: none;
        color: #fff;
        font-size: 1.2rem;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .service-guidance-modal .modal-close:hover {
        background: rgba(255, 255, 255, 0.1);
        color: var(--neon-cyan);
    }

    .service-guidance-modal .modal-footer {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-top: 2rem;
        padding-top: 1rem;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .btn-secondary {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        transition: all 0.3s ease;
    }

    .btn-secondary:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.4);
    }
`;
document.head.appendChild(style);

// Note: Manual initialization required - no automatic initialization
// Use: window.squareBooking = new SquareBookingManager(); when needed

// Global booking functions for easy integration
window.openSquareBooking = function(serviceId = null, serviceName = 'General Booking') {
    if (window.squareBooking) {
        if (serviceId) {
            window.squareBooking.openBookingFlow(serviceId, serviceName);
        } else {
            window.squareBooking.openDirectBookingLink('general', serviceName);
        }
    } else {
        // Fallback if booking manager not loaded
        window.open('https://book.squareup.com/appointments/dp9qk14alq4461/location/LQDVMV8ZMNWDD', '_blank');
    }
};

// Enhanced booking function with service mapping
window.bookService = function(serviceName, serviceId = null) {
    if (window.squareBooking) {
        // Try to find service ID by name if not provided
        if (!serviceId && serviceName) {
            const mapping = window.squareBooking.serviceMapping;
            serviceId = Object.keys(mapping).find(id =>
                mapping[id].name.toLowerCase() === serviceName.toLowerCase() ||
                mapping[id].squareServiceName.toLowerCase() === serviceName.toLowerCase()
            );
        }

        window.squareBooking.openBookingFlow(serviceId || 'general', serviceName);
    } else {
        window.openSquareBooking();
    }
};

// Service lookup function
window.getServiceInfo = function(serviceId) {
    if (window.squareBooking && window.squareBooking.serviceMapping[serviceId]) {
        return window.squareBooking.serviceMapping[serviceId];
    }
    return null;
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SquareBookingManager;
}
