<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Braids Shop - GetTwisted Studios | Protective Styling & Cultural Artistry</title>
    <meta name="description" content="Professional braiding services in Philadelphia. Box braids, cornrows, protective styling, and cultural hair artistry celebrating heritage and creativity.">
    <meta name="keywords" content="braids, box braids, cornrows, protective styling, knotless braids, feed-ins, natural hair, Philadelphia, GetTwisted">
    
    <!-- Open Graph Meta -->
    <meta property="og:title" content="Braids Shop - GetTwisted Studios">
    <meta property="og:description" content="Protective styling that celebrates culture and creativity. From classic braids to modern protective styles.">
    <meta property="og:image" content="https://gettwistedlocs.com/assets/common/hero.jpg">
    <meta property="og:url" content="https://gettwistedlocs.com/braids/">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Fredoka+One:wght@400&family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Design System -->
    <link rel="stylesheet" href="/shared/css/design-system.css">
    
    <!-- Custom Styles -->
    <style>
        .braids-hero-gradient {
            background: linear-gradient(135deg, #ec4899 0%, #fbbf24 100%);
            position: relative;
        }
        
        .braids-hero-gradient::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('../assets/images/braids-texture.jpg') center/cover;
            opacity: 0.2;
            z-index: 1;
        }
        
        .braids-hero-gradient > * {
            position: relative;
            z-index: 2;
        }

        /* Override z-index for carousel elements */

        
        .service-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .service-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(236, 72, 153, 0.15);
        }
        
        .floating-element {
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(5deg); }
        }
        
        .gallery-item {
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .gallery-item:hover {
            transform: scale(1.05);
        }
        
        .gallery-item img {
            transition: all 0.3s ease;
        }
        
        .gallery-item:hover img {
            transform: scale(1.1);
        }

        /* Custom Booking Interface Styles */
        .custom-booking-container {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(236, 72, 153, 0.3);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border-radius: 1rem;
            padding: 2rem;
            margin: 2rem 0;
            position: relative;
            overflow: hidden;
        }

        .custom-booking-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(236, 72, 153, 0.05), transparent);
            animation: shimmer 3s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes shimmer {
            0% { box-shadow: 0 0 20px rgba(236, 72, 153, 0.3); }
            100% { box-shadow: 0 0 30px rgba(236, 72, 153, 0.6); }
        }

        /* Booking Progress Indicator */
        .booking-progress {
            position: relative;
            z-index: 10;
        }

        .step-indicator {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(236, 72, 153, 0.1);
            border: 2px solid rgba(236, 72, 153, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step-indicator.active .step-circle {
            background: linear-gradient(135deg, #ec4899, #a855f7);
            border-color: #ec4899;
            color: #fff;
            box-shadow: 0 0 20px rgba(236, 72, 153, 0.5);
        }

        .step-indicator.completed .step-circle {
            background: linear-gradient(135deg, #db2777, #be185d);
            border-color: #db2777;
            color: #fff;
        }

        .step-label {
            margin-top: 0.5rem;
            font-size: 0.875rem;
            color: #6b7280;
            transition: color 0.3s ease;
        }

        .step-indicator.active .step-label {
            color: #ec4899;
        }

        .step-line {
            flex: 1;
            height: 2px;
            background: rgba(236, 72, 153, 0.2);
            margin: 0 1rem;
            position: relative;
            top: -20px;
        }

        /* Booking Steps */
        .booking-step {
            position: relative;
            z-index: 10;
        }

        .booking-step.hidden {
            display: none;
        }

        /* Service Cards in Booking */
        .booking-service-card {
            background: rgba(236, 72, 153, 0.05);
            border: 1px solid rgba(236, 72, 153, 0.2);
            border-radius: 0.75rem;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .booking-service-card:hover {
            border-color: rgba(236, 72, 153, 0.5);
            background: rgba(236, 72, 153, 0.08);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(236, 72, 153, 0.2);
        }

        .booking-service-card.selected {
            border-color: #ec4899;
            background: rgba(236, 72, 153, 0.1);
            box-shadow: 0 0 20px rgba(236, 72, 153, 0.3);
        }

        /* Date Picker */
        .date-picker-container {
            position: relative;
        }

        .date-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 0.25rem;
            margin-top: 1rem;
        }

        .date-cell {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0.375rem;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.875rem;
            color: #374151;
        }

        .date-cell:hover {
            background: rgba(236, 72, 153, 0.1);
        }

        .date-cell.selected {
            background: linear-gradient(135deg, #ec4899, #a855f7);
            color: #fff;
            font-weight: bold;
        }

        .date-cell.disabled {
            color: #9ca3af;
            cursor: not-allowed;
        }

        /* Time Slots */
        .time-slot {
            background: rgba(236, 72, 153, 0.05);
            border: 1px solid rgba(236, 72, 153, 0.2);
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            color: #374151;
        }

        .time-slot:hover {
            border-color: rgba(236, 72, 153, 0.5);
            background: rgba(236, 72, 153, 0.08);
        }

        .time-slot.selected {
            border-color: #ec4899;
            background: rgba(236, 72, 153, 0.1);
            color: #ec4899;
        }

        .time-slot.unavailable {
            opacity: 0.5;
            cursor: not-allowed;
            background: rgba(239, 68, 68, 0.1);
            border-color: rgba(239, 68, 68, 0.3);
        }

        /* Hero Carousel Styles */
        .hero-carousel-container {
            position: relative;
            width: 100%;
            height: 100vh;
            overflow: hidden;
            z-index: 10;
        }

        .hero-slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 5;
        }

        .hero-slide.active {
            opacity: 1;
            transform: translateX(0);
            z-index: 6;
        }

        .hero-slide.prev {
            transform: translateX(-100%);
        }

        /* Carousel Navigation Dots */
        .carousel-nav {
            display: flex;
            gap: 12px;
            z-index: 20;
        }

        .nav-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.4);
            border: 2px solid rgba(255, 255, 255, 0.6);
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .nav-dot:hover {
            background: rgba(255, 255, 255, 0.6);
            transform: scale(1.1);
        }

        .nav-dot.active {
            background: rgba(236, 72, 153, 0.8);
            border-color: rgba(236, 72, 153, 1);
            box-shadow: 0 0 15px rgba(236, 72, 153, 0.5);
        }

        /* Carousel Arrows */
        .carousel-arrow {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .carousel-arrow:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .carousel-arrow:active {
            transform: scale(0.95);
        }

        /* Auto-play indicator */
        .carousel-progress {
            position: absolute;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 2px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 1px;
            overflow: hidden;
            z-index: 20;
        }

        .carousel-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #ec4899, #a855f7);
            width: 0%;
            transition: width 0.1s linear;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .carousel-arrow {
                width: 40px;
                height: 40px;
            }

            .carousel-arrow i {
                font-size: 1.2rem;
            }

            .nav-dot {
                width: 10px;
                height: 10px;
            }
        }
    </style>
</head>
<body class="bg-neutral-50">
    <!-- Navigation -->
    <nav class="fixed top-0 w-full z-50 glass-strong">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <a href="../" class="font-display text-2xl text-white hover:text-pink-300 transition-smooth">
                        <img src="../assets/common/gettwisted-logo.png" alt="Get Twisted Hair Studio Logo" class="logo">
                    </a>
                    <span class="text-pink-300">|</span>
                    <span class="font-accent text-lg text-pink-200">Braids Shop</span>
                </div>
                <div class="hidden md:flex space-x-8">
                    <a href="#services" class="text-white hover:text-pink-300 transition-smooth">Services</a>
                    <a href="../gallery.html" class="text-white hover:text-pink-300 transition-smooth">Gallery</a>
                    <a href="../stylists.html" class="text-white hover:text-pink-300 transition-smooth">Stylists</a>
                    <a href="#about" class="text-white hover:text-pink-300 transition-smooth">About</a>
                    <a href="#contact" class="text-white hover:text-pink-300 transition-smooth">Contact</a>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="openSquareBooking()" class="btn btn-sm bg-pink-600 text-white hover:bg-pink-700">
                        Book Now
                    </button>
                    <button id="mobile-menu-btn" class="md:hidden text-white">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Mobile Menu -->
    <div id="mobile-menu" class="fixed top-0 left-0 w-full h-full bg-black bg-opacity-90 z-40 hidden md:hidden">
        <div class="flex flex-col items-center justify-center h-full space-y-8">
            <button id="mobile-menu-close" class="absolute top-6 right-6 text-white text-2xl">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
            <a href="#services" class="text-white text-2xl hover:text-pink-300 transition-smooth">Services</a>
            <a href="../gallery.html" class="text-white text-2xl hover:text-pink-300 transition-smooth">Gallery</a>
            <a href="../stylists.html" class="text-white text-2xl hover:text-pink-300 transition-smooth">Stylists</a>
            <a href="#about" class="text-white text-2xl hover:text-pink-300 transition-smooth">About</a>
            <a href="#contact" class="text-white text-2xl hover:text-pink-300 transition-smooth">Contact</a>
            <button onclick="openSquareBooking()" class="btn bg-pink-600 text-white hover:bg-pink-700 px-8 py-3 text-xl">
                Book Now
            </button>
        </div>
    </div>

    <!-- Hero Carousel Section -->
    <section class="braids-hero-gradient min-h-screen flex items-center justify-center relative overflow-hidden">
        <div class="hero-carousel-container w-full h-full relative">

            <!-- Slide 1: Original Content -->
            <div class="hero-slide active" data-slide="1">
                <div class="container mx-auto px-4 text-center h-screen flex items-center justify-center">
                    <div class="animate-fade-in-up">
                        <div class="text-6xl mb-6 floating-element">💗</div>
                        <h1 class="font-display text-6xl md:text-8xl text-white mb-6">
                            Braids Shop
                        </h1>
                        <p class="font-accent text-2xl md:text-3xl text-white mb-4">
                            Where Culture Meets Creativity
                        </p>
                        <p class="text-xl text-white text-opacity-90 mb-12 max-w-2xl mx-auto">
                            Protective styling that celebrates heritage and artistic expression. From classic braids to modern protective styles.
                        </p>
                        <div class="flex flex-col sm:flex-row gap-4 justify-center">
                            <a href="#services" class="btn btn-lg bg-white text-pink-600 hover:bg-pink-50">
                                View Services
                            </a>
                            <button id="book-consultation-btn" class="btn btn-lg glass text-white hover:bg-white hover:text-pink-600">
                                Book Consultation
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 2: Back-to-School Special -->
            <div class="hero-slide" data-slide="2">
                <div class="container mx-auto px-4 text-center h-screen flex items-center justify-center">
                    <div class="animate-fade-in-up">
                        <div class="text-6xl mb-6 floating-element">🎒</div>
                        <h1 class="font-display text-5xl md:text-7xl text-white mb-6">
                            Back-to-School Special
                        </h1>
                        <p class="font-accent text-2xl md:text-3xl text-white mb-4">
                            Fresh Braids for a Fresh Start
                        </p>
                        <p class="text-xl text-white text-opacity-90 mb-8 max-w-2xl mx-auto">
                            Get your child ready for the new school year with our protective styling packages.
                            Professional braids that last all semester long.
                        </p>
                        <div class="bg-white/20 backdrop-blur-sm rounded-2xl p-6 mb-8 max-w-lg mx-auto border border-white/30">
                            <div class="text-3xl font-bold text-white mb-2">20% OFF</div>
                            <div class="text-lg text-white/90">All Student Braiding Services</div>
                            <div class="text-sm text-white/80 mt-2">Valid through September 30th</div>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-4 justify-center">
                            <a href="#services" class="btn btn-lg bg-white text-pink-600 hover:bg-pink-50">
                                View Student Packages
                            </a>
                            <button id="book-special-btn" class="btn btn-lg glass text-white hover:bg-white hover:text-pink-600">
                                Book Special Now
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Slide 3: Premium Service Advertisement -->
            <div class="hero-slide" data-slide="3">
                <div class="container mx-auto px-4 text-center h-screen flex items-center justify-center">
                    <div class="animate-fade-in-up">
                        <div class="text-6xl mb-6 floating-element">👑</div>
                        <h1 class="font-display text-5xl md:text-7xl text-white mb-6">
                            Premium Braiding Experience
                        </h1>
                        <p class="font-accent text-2xl md:text-3xl text-white mb-4">
                            Luxury Meets Tradition
                        </p>
                        <p class="text-xl text-white text-opacity-90 mb-8 max-w-2xl mx-auto">
                            Experience our signature VIP braiding service with premium hair extensions,
                            complimentary scalp treatment, and personalized styling consultation.
                        </p>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8 max-w-3xl mx-auto">
                            <div class="bg-white/20 backdrop-blur-sm rounded-xl p-4 border border-white/30">
                                <div class="text-2xl mb-2">✨</div>
                                <div class="text-white font-semibold">Premium Extensions</div>
                            </div>
                            <div class="bg-white/20 backdrop-blur-sm rounded-xl p-4 border border-white/30">
                                <div class="text-2xl mb-2">💆‍♀️</div>
                                <div class="text-white font-semibold">Scalp Treatment</div>
                            </div>
                            <div class="bg-white/20 backdrop-blur-sm rounded-xl p-4 border border-white/30">
                                <div class="text-2xl mb-2">🎨</div>
                                <div class="text-white font-semibold">Custom Styling</div>
                            </div>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-4 justify-center">
                            <a href="#services" class="btn btn-lg bg-white text-pink-600 hover:bg-pink-50">
                                Learn More
                            </a>
                            <button id="book-premium-btn" class="btn btn-lg glass text-white hover:bg-white hover:text-pink-600">
                                Book VIP Service
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Carousel Navigation -->
            <div class="carousel-nav absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-3 z-20">
                <button class="nav-dot active" data-slide="1"></button>
                <button class="nav-dot" data-slide="2"></button>
                <button class="nav-dot" data-slide="3"></button>
            </div>

            <!-- Carousel Arrows -->
            <button class="carousel-arrow carousel-prev absolute left-4 top-1/2 transform -translate-y-1/2 z-20">
                <i class="fas fa-chevron-left text-2xl text-white"></i>
            </button>
            <button class="carousel-arrow carousel-next absolute right-4 top-1/2 transform -translate-y-1/2 z-20">
                <i class="fas fa-chevron-right text-2xl text-white"></i>
            </button>
        </div>

        <!-- Floating Elements -->
        <div class="absolute top-20 left-10 w-16 h-16 bg-white bg-opacity-10 rounded-full floating-element" style="animation-delay: 0s;"></div>
        <div class="absolute top-40 right-20 w-12 h-12 bg-white bg-opacity-10 rounded-full floating-element" style="animation-delay: 2s;"></div>
        <div class="absolute bottom-40 left-20 w-20 h-20 bg-white bg-opacity-10 rounded-full floating-element" style="animation-delay: 4s;"></div>
    </section>

    <!-- Services Preview Section -->
    <section id="services" class="section-padding bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="font-accent text-5xl font-bold mb-6 text-gray-800">Our Braiding Services</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Professional protective styling with cultural respect and artistic flair. Each braid tells a story of heritage and beauty.
                </p>
            </div>
            
            <!-- System Status Notice -->
            <div id="system-status-notice" class="bg-gradient-to-r from-pink-500/20 to-purple-500/20 border border-pink-500/30 rounded-lg p-4 mb-6 max-w-4xl mx-auto">
                <div class="flex items-center space-x-3">
                    <i id="status-icon" class="fas fa-sync-alt fa-spin text-pink-400 text-xl"></i>
                    <div>
                        <h4 id="status-title" class="text-pink-400 font-bold">Connecting to Square API...</h4>
                        <p id="status-message" class="text-gray-600 text-sm">Checking connection to Square booking system...</p>
                    </div>
                </div>
            </div>

            <!-- Dynamic Services Grid -->
            <div id="services-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Services will be loaded dynamically -->
                <div class="text-center py-12 col-span-full">
                    <div class="animate-spin inline-block w-8 h-8 border-4 border-pink-400 border-t-transparent rounded-full mb-4"></div>
                    <p class="text-gray-600">Loading braiding services...</p>
                </div>
            </div>

            <div class="text-center mt-12">
                <button id="book-service-btn" class="btn btn-lg bg-pink-600 text-white hover:bg-pink-700 px-8 py-4 rounded-lg" disabled>
                    <i class="fas fa-calendar-plus mr-2"></i>Book Appointment
                </button>
            </div>
        </div>
    </section>

    <!-- Custom Booking Interface -->
    <section id="booking-interface" class="section-padding bg-gradient-to-br from-pink-50 to-purple-50" style="display: none;">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="font-accent text-5xl font-bold mb-6 text-gray-800">Book Your Braiding Service</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Complete your booking with our secure Square integration
                </p>
            </div>

            <!-- Custom Square Booking Interface -->
            <div class="custom-booking-container max-w-4xl mx-auto bg-white rounded-2xl shadow-xl p-8">
                <div class="text-center mb-6">
                    <div class="inline-flex items-center space-x-2 bg-gradient-to-r from-pink-500/20 to-purple-500/20 px-4 py-2 rounded-full border border-pink-500/30">
                        <i class="fas fa-shield-alt text-pink-600"></i>
                        <span class="text-sm text-gray-700">Secure booking powered by Square</span>
                    </div>
                </div>

                <!-- Multi-Step Booking Interface -->
                <div id="custom-booking-interface" class="min-h-[600px] bg-white/5 rounded-lg border border-pink-500/30 p-6">

                    <!-- Step Progress Indicator -->
                    <div class="booking-progress mb-8">
                        <div class="flex justify-between items-center">
                            <div class="step-indicator active" data-step="1">
                                <div class="step-circle">1</div>
                                <span class="step-label">Service</span>
                            </div>
                            <div class="step-line"></div>
                            <div class="step-indicator" data-step="2">
                                <div class="step-circle">2</div>
                                <span class="step-label">Time</span>
                            </div>
                            <div class="step-line"></div>
                            <div class="step-indicator" data-step="3">
                                <div class="step-circle">3</div>
                                <span class="step-label">Details</span>
                            </div>
                            <div class="step-line"></div>
                            <div class="step-indicator" data-step="4">
                                <div class="step-circle">4</div>
                                <span class="step-label">Confirm</span>
                            </div>
                        </div>
                    </div>

                    <!-- Step 1: Service Selection -->
                    <div id="step-1" class="booking-step active">
                        <h3 class="text-2xl font-bold text-gray-800 mb-6 text-center">Choose Your Braiding Service</h3>
                        <div id="booking-services-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <!-- Services will be loaded dynamically -->
                            <div class="text-center py-12 col-span-full">
                                <div class="animate-spin inline-block w-8 h-8 border-4 border-pink-400 border-t-transparent rounded-full mb-4"></div>
                                <p class="text-gray-600">Loading services...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Time Selection -->
                    <div id="step-2" class="booking-step hidden">
                        <h3 class="text-2xl font-bold text-gray-800 mb-6 text-center">Select Date & Time</h3>
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Date Picker -->
                            <div class="date-picker-container">
                                <h4 class="text-lg font-semibold text-pink-600 mb-4">Choose Date</h4>
                                <div id="date-picker" class="bg-gray-50 rounded-lg p-4 border border-pink-500/30">
                                    <!-- Date picker will be generated -->
                                </div>
                            </div>
                            <!-- Time Slots -->
                            <div class="time-slots-container">
                                <h4 class="text-lg font-semibold text-pink-600 mb-4">Available Times</h4>
                                <div id="time-slots" class="space-y-2 max-h-96 overflow-y-auto">
                                    <!-- Time slots will be loaded dynamically -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Customer Details -->
                    <div id="step-3" class="booking-step hidden">
                        <h3 class="text-2xl font-bold text-gray-800 mb-6 text-center">Your Information</h3>
                        <div class="max-w-2xl mx-auto">
                            <form id="customer-form" class="space-y-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="form-group">
                                        <label for="firstName" class="block text-sm font-medium text-pink-600 mb-2">First Name *</label>
                                        <input type="text" id="firstName" name="firstName" required
                                               class="w-full px-4 py-3 bg-gray-50 border border-pink-500/30 rounded-lg text-gray-800 placeholder-gray-400 focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20 transition-all">
                                    </div>
                                    <div class="form-group">
                                        <label for="lastName" class="block text-sm font-medium text-pink-600 mb-2">Last Name *</label>
                                        <input type="text" id="lastName" name="lastName" required
                                               class="w-full px-4 py-3 bg-gray-50 border border-pink-500/30 rounded-lg text-gray-800 placeholder-gray-400 focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20 transition-all">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="email" class="block text-sm font-medium text-pink-600 mb-2">Email Address *</label>
                                    <input type="email" id="email" name="email" required
                                           class="w-full px-4 py-3 bg-gray-50 border border-pink-500/30 rounded-lg text-gray-800 placeholder-gray-400 focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20 transition-all">
                                </div>
                                <div class="form-group">
                                    <label for="phone" class="block text-sm font-medium text-pink-600 mb-2">Phone Number *</label>
                                    <input type="tel" id="phone" name="phone" required
                                           class="w-full px-4 py-3 bg-gray-50 border border-pink-500/30 rounded-lg text-gray-800 placeholder-gray-400 focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20 transition-all">
                                </div>
                                <div class="form-group">
                                    <label for="notes" class="block text-sm font-medium text-pink-600 mb-2">Special Requests (Optional)</label>
                                    <textarea id="notes" name="notes" rows="3"
                                              class="w-full px-4 py-3 bg-gray-50 border border-pink-500/30 rounded-lg text-gray-800 placeholder-gray-400 focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20 transition-all"
                                              placeholder="Any special requests or notes for your braiding service..."></textarea>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Step 4: Confirmation -->
                    <div id="step-4" class="booking-step hidden">
                        <h3 class="text-2xl font-bold text-gray-800 mb-6 text-center">Confirm Your Appointment</h3>
                        <div class="max-w-2xl mx-auto">
                            <div id="booking-summary" class="bg-gray-50 rounded-lg p-6 border border-pink-500/30 mb-6">
                                <!-- Booking summary will be populated -->
                            </div>
                            <div class="text-center">
                                <button id="confirm-booking" class="btn btn-lg bg-pink-600 text-white hover:bg-pink-700 px-8 py-4 text-lg">
                                    <i class="fas fa-check-circle mr-2"></i>
                                    Confirm Appointment
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="booking-navigation mt-8 flex justify-between">
                        <button id="prev-step" class="btn bg-gray-500 text-white px-6 py-3 rounded-lg hidden">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Previous
                        </button>
                        <button id="next-step" class="btn bg-pink-600 text-white hover:bg-pink-700 px-6 py-3 rounded-lg ml-auto">
                            Next
                            <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>

                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Preview Section -->
    <section id="gallery" class="section-padding braids-hero-gradient">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="font-accent text-5xl font-bold mb-6 text-white">Our Artistry</h2>
                <p class="text-xl text-white text-opacity-90 max-w-3xl mx-auto">
                    Every braid is a work of art. See the creativity, precision, and cultural celebration in our protective styling.
                </p>
            </div>
            
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <div class="gallery-item rounded-xl overflow-hidden">
                    <img src="../assets/shops/braiding/styles/rs1.jpg" alt="Box braids" class="w-full h-48 object-cover">
                </div>
                <div class="gallery-item rounded-xl overflow-hidden">
                    <img src="../assets/shops/braiding/styles/rs2.jpg" alt="Cornrows design" class="w-full h-48 object-cover">
                </div>
                <div class="gallery-item rounded-xl overflow-hidden">
                    <img src="../assets/shops/braiding/styles/rs3.jpg" alt="Protective styling" class="w-full h-48 object-cover">
                </div>
                <div class="gallery-item rounded-xl overflow-hidden">
                    <img src="../assets/shops/braiding/styles/rs4.jpg" alt="Creative braids" class="w-full h-48 object-cover">
                </div>
            </div>
            
            <div class="text-center mt-12">
                <a href="../gallery.html" class="btn btn-lg glass text-white hover:bg-white hover:text-pink-600">
                    View Full Gallery →
                </a>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="section-padding bg-white">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <h2 class="font-accent text-5xl font-bold mb-6 text-gray-800">Why Choose Our Braids Shop?</h2>
                    <p class="text-xl text-gray-600 mb-8">
                        Braiding is an ancient art form that connects us to our heritage while expressing individual creativity. Our skilled artists honor this tradition while embracing modern techniques and styles.
                    </p>

                    <div class="space-y-6">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <span class="text-xl">🎨</span>
                            </div>
                            <div>
                                <h3 class="font-accent text-xl font-bold mb-2 text-gray-800">Cultural Artistry</h3>
                                <p class="text-gray-600">Honoring traditional techniques while embracing creative innovation.</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <span class="text-xl">🛡️</span>
                            </div>
                            <div>
                                <h3 class="font-accent text-xl font-bold mb-2 text-gray-800">Hair Protection</h3>
                                <p class="text-gray-600">Gentle techniques that protect and promote healthy hair growth.</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <span class="text-xl">⏰</span>
                            </div>
                            <div>
                                <h3 class="font-accent text-xl font-bold mb-2 text-gray-800">Long-Lasting Styles</h3>
                                <p class="text-gray-600">Quality braiding that maintains beauty and integrity for weeks.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="relative">
                    <div class="glass-light rounded-2xl p-8">
                        <img src="../assets/shops/braiding/styles/rs5.jpg" alt="Braiding artistry process" class="w-full rounded-xl">
                    </div>
                    <div class="absolute -top-4 -right-4 w-24 h-24 bg-pink-600 rounded-full flex items-center justify-center">
                        <span class="text-white text-2xl">💗</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stylists Section -->
    <section id="stylists" class="section-padding bg-neutral-100">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="font-accent text-5xl font-bold mb-6 text-gray-800">Meet Our Braiding Artists</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Our certified braiding specialists combine traditional techniques with modern creativity to create stunning protective styles.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="glass-light rounded-2xl p-8 text-center">
                    <div class="w-24 h-24 rounded-full mx-auto mb-6 overflow-hidden">
                        <img src="../assets/stylists/sasha.png" alt="Sasha" class="w-full h-full object-cover">
                    </div>
                    <h3 class="font-accent text-2xl font-bold mb-2 text-neon-pink">Sasha</h3>
                    <p class="text-pink-600 font-medium mb-4">Braid Specialist</p>
                    <p class="text-gray-600 mb-6">
                        Expertise in box braids, knotless braids, and protective styling.
                    </p>
                    <a href="../stylists.html" class="btn-holographic px-6 py-3 rounded-lg">
                        <i class="fas fa-user mr-2"></i>View Profile
                    </a>
                </div>

                <div class="glass-light rounded-2xl p-8 text-center">
                    <div class="w-24 h-24 rounded-full mx-auto mb-6 overflow-hidden">
                        <img src="../assets/stylists/sheryl.png" alt="Sheryl Williams" class="w-full h-full object-cover">
                    </div>
                    <h3 class="font-accent text-2xl font-bold mb-2 text-neon-purple">Sheryl Williams</h3>
                    <p class="text-yellow-600 font-medium mb-4">Master Loctician</p>
                    <p class="text-gray-600 mb-6">
                        10+ years of experience with natural hair and protective styling.
                    </p>
                    <a href="../stylists.html" class="btn-holographic px-6 py-3 rounded-lg">
                        <i class="fas fa-user mr-2"></i>View Profile
                    </a>
                </div>

                <div class="glass-light rounded-2xl p-8 text-center">
                    <div class="w-24 h-24 rounded-full mx-auto mb-6 overflow-hidden">
                        <img src="../assets/stylists/rachel.png" alt="Rachel" class="w-full h-full object-cover">
                    </div>
                    <h3 class="font-accent text-2xl font-bold mb-2 text-neon-cyan">Rachel</h3>
                    <p class="text-pink-600 font-medium mb-4">Natural Hair Specialist</p>
                    <p class="text-gray-600 mb-6">
                        Specializes in healthy hair care, styling, and color treatments.
                    </p>
                    <a href="../stylists.html" class="btn-holographic px-6 py-3 rounded-lg">
                        <i class="fas fa-user mr-2"></i>View Profile
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact & Booking Section -->
    <section id="contact" class="section-padding braids-hero-gradient">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="font-accent text-5xl font-bold mb-6 text-white">Ready for Your Protective Style?</h2>
                <p class="text-xl text-white text-opacity-90 max-w-3xl mx-auto">
                    Book your consultation today and let's create a beautiful protective style that celebrates your heritage and personal style.
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <div class="glass rounded-2xl p-8">
                    <h3 class="font-accent text-3xl font-bold mb-6 text-white">Book Your Appointment</h3>
                    <div class="space-y-4">
                        <button onclick="openSquareBooking()" class="btn btn-lg bg-white text-pink-600 hover:bg-pink-50 w-full">
                            📅 Online Booking
                        </button>
                        <a href="tel:+16102880343" class="btn btn-lg glass text-white hover:bg-white hover:text-pink-600 w-full">
                            📞 Call (*************
                        </a>
                        <a href="mailto:<EMAIL>" class="btn btn-lg glass text-white hover:bg-white hover:text-pink-600 w-full">
                            ✉️ Email Us
                        </a>
                    </div>
                </div>

                <div class="glass rounded-2xl p-8">
                    <h3 class="font-accent text-3xl font-bold mb-6 text-white">Visit Our Studio</h3>
                    <div class="space-y-4 text-white">
                        <div class="flex items-start space-x-3">
                            <span class="text-xl">📍</span>
                            <div>
                                <p class="font-medium">Teaneck, NJ</p>
                                <p class="text-white text-opacity-80">Pottstown, PA</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-3">
                            <span class="text-xl">🕒</span>
                            <div>
                                <p class="font-medium">Studio Hours</p>
                                <p class="text-white text-opacity-80">Mon-Sat: 8AM-8PM</p>
                                <p class="text-white text-opacity-80">Sunday: 10AM-6PM</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-3">
                            <span class="text-xl">💳</span>
                            <div>
                                <p class="font-medium">Payment Options</p>
                                <p class="text-white text-opacity-80">Cash, Card, Digital Payments</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <h3 class="font-display text-2xl mb-4">GetTwisted Studios</h3>
                    <p class="text-gray-400 mb-4">Braids Shop - Where culture meets creativity</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-pink-400 hover:text-pink-300">Instagram</a>
                        <a href="#" class="text-pink-400 hover:text-pink-300">Facebook</a>
                        <a href="#" class="text-pink-400 hover:text-pink-300">TikTok</a>
                    </div>
                </div>

                <div>
                    <h4 class="font-accent text-lg font-bold mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="services.html" class="text-gray-400 hover:text-white">Services</a></li>
                        <li><a href="gallery.html" class="text-gray-400 hover:text-white">Gallery</a></li>
                        <li><a href="stylists.html" class="text-gray-400 hover:text-white">Stylists</a></li>
                        <li><a href="../" class="text-gray-400 hover:text-white">All Shops</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="font-accent text-lg font-bold mb-4">Other Shops</h4>
                    <ul class="space-y-2">
                        <li><a href="../locs/" class="text-purple-400 hover:text-purple-300">Locs Studio</a></li>
                        <li><a href="../barber/" class="text-green-400 hover:text-green-300">Barber Shop</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center">
                <p class="text-gray-400">&copy; 2024 GetTwisted Studios. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../js/service-manager.js"></script>
    <script>
        // Initialize service manager for braids shop
        const serviceManager = new ServiceManager('braids');

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add scroll effect to navigation
        window.addEventListener('scroll', () => {
            const nav = document.querySelector('nav');
            if (window.scrollY > 100) {
                nav.classList.add('bg-black', 'bg-opacity-80');
            } else {
                nav.classList.remove('bg-black', 'bg-opacity-80');
            }
        });

        // Mobile menu functionality
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');
        const mobileMenuClose = document.getElementById('mobile-menu-close');

        if (mobileMenuBtn && mobileMenu && mobileMenuClose) {
            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            });

            mobileMenuClose.addEventListener('click', () => {
                mobileMenu.classList.add('hidden');
                document.body.style.overflow = 'auto';
            });

            // Close mobile menu when clicking on links
            mobileMenu.querySelectorAll('a').forEach(link => {
                link.addEventListener('click', () => {
                    mobileMenu.classList.add('hidden');
                    document.body.style.overflow = 'auto';
                });
            });
        }

        // Square API Configuration (same as other pages)
        class SquareAPIClient {
            constructor() {
                this.config = {
                    proxyURL: 'https://square-proxy-server.onrender.com',
                    enableFallback: false,
                    timeout: 30000
                };
                this.isProxyMode = true;
                this.proxyAvailable = null;
            }

            async makeRequest(endpoint, method = 'GET', data = null) {
                console.log(`Square API Call: ${method} ${endpoint}`, data);

                if (this.proxyAvailable === null) {
                    this.proxyAvailable = await this.checkProxyHealth();
                }

                if (this.proxyAvailable && this.isProxyMode) {
                    try {
                        return await this.makeProxyRequest(endpoint, method, data);
                    } catch (error) {
                        console.warn('Proxy request failed, falling back to demo mode:', error);
                        this.proxyAvailable = false;
                        if (!this.config.enableFallback) {
                            throw error;
                        }
                    }
                }

                console.log('Using demo mode for Square API simulation');
                return await this.makeDemoRequest(endpoint, method, data);
            }

            async checkProxyHealth() {
                try {
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 5000);

                    const response = await fetch(`${this.config.proxyURL}/health`, {
                        method: 'GET',
                        signal: controller.signal
                    });

                    clearTimeout(timeoutId);

                    if (response.ok) {
                        const result = await response.json();
                        console.log('Proxy server health check passed:', result);
                        return true;
                    } else {
                        console.warn('Health check failed with status:', response.status, response.statusText);
                        return false;
                    }
                } catch (error) {
                    console.error('Proxy server health check encountered an error:', error);
                    return false;
                }
            }

            async makeProxyRequest(endpoint, method = 'GET', data = null) {
                const url = `${this.config.proxyURL}/api/square${endpoint}`;
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

                const options = {
                    method,
                    headers: { 'Content-Type': 'application/json' },
                    signal: controller.signal
                };

                if (data && (method === 'POST' || method === 'PUT')) {
                    options.body = JSON.stringify(data);
                }

                try {
                    const response = await fetch(url, options);
                    clearTimeout(timeoutId);
                    const result = await response.json();

                    if (!response.ok) {
                        throw new Error(result.error || `HTTP ${response.status}: ${response.statusText}`);
                    }

                    console.log('Proxy API response:', result);
                    return result;
                } catch (error) {
                    clearTimeout(timeoutId);
                    console.error('Proxy API Error:', error);
                    throw error;
                }
            }

            async makeDemoRequest(endpoint, method = 'GET', data = null) {
                await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

                if (endpoint.includes('/catalog/search')) {
                    return this.simulateCatalogResponse();
                } else if (endpoint.includes('/bookings/availability/search')) {
                    return this.simulateAvailabilityResponse();
                } else if (endpoint.includes('/bookings') && method === 'POST') {
                    return this.simulateBookingResponse(data);
                } else if (endpoint.includes('/customers') && method === 'POST') {
                    return this.simulateCustomerResponse(data);
                }

                throw new Error('API endpoint not implemented in demo mode');
            }

            simulateCatalogResponse() {
                return {
                    objects: [
                        {
                            id: 'ITEM_7',
                            type: 'ITEM',
                            item_data: {
                                name: 'Box Braids & Twists',
                                description: 'Classic protective styling with modern techniques. Knotless options available',
                                variations: [{
                                    id: 'VAR_7',
                                    item_variation_data: {
                                        price_money: { amount: 20000, currency: 'USD' }
                                    }
                                }]
                            }
                        },
                        {
                            id: 'ITEM_8',
                            type: 'ITEM',
                            item_data: {
                                name: 'Cornrows & Feed-ins',
                                description: 'Intricate patterns and designs that showcase artistry while protecting your natural hair',
                                variations: [{
                                    id: 'VAR_8',
                                    item_variation_data: {
                                        price_money: { amount: 12000, currency: 'USD' }
                                    }
                                }]
                            }
                        },
                        {
                            id: 'ITEM_9',
                            type: 'ITEM',
                            item_data: {
                                name: 'Protective Styling',
                                description: 'Custom protective styles including goddess braids, fulani braids, and creative updos',
                                variations: [{
                                    id: 'VAR_9',
                                    item_variation_data: {
                                        price_money: { amount: 15000, currency: 'USD' }
                                    }
                                }]
                            }
                        }
                    ]
                };
            }

            async getServices() {
                try {
                    const response = await this.makeRequest('/catalog/search', 'POST', {
                        query: {
                            prefixQuery: {
                                attributePrefix: ''
                            }
                        },
                        limit: 100
                    });

                    return response.objects || [];
                } catch (error) {
                    console.error('Error fetching services:', error);
                    throw error;
                }
            }
        }

        // Braiding Booking Interface Controller
        class BraidingBookingInterface {
            constructor() {
                this.currentStep = 1;
                this.selectedService = null;
                this.selectedDate = null;
                this.selectedTime = null;
                this.customerData = {};
                this.availableServices = [];
                this.availableSlots = [];
                this.squareAPI = new SquareAPIClient();

                this.init();
                this.updateSystemStatus();
            }

            async updateSystemStatus() {
                const statusIcon = document.getElementById('status-icon');
                const statusTitle = document.getElementById('status-title');
                const statusMessage = document.getElementById('status-message');
                const statusNotice = document.getElementById('system-status-notice');

                try {
                    const isProxyAvailable = await this.squareAPI.checkProxyHealth();

                    if (isProxyAvailable) {
                        statusIcon.className = 'fas fa-check-circle text-pink-400 text-xl';
                        statusTitle.textContent = 'Live Square Integration Active';
                        statusMessage.textContent = 'Connected to Square API. Real appointments will be created in your Square dashboard.';
                        statusNotice.className = statusNotice.className.replace('pink-500', 'green-500');
                    } else {
                        statusIcon.className = 'fas fa-exclamation-triangle text-yellow-400 text-xl';
                        statusTitle.textContent = 'Demo Mode Active';
                        statusMessage.textContent = 'Square API proxy unavailable. Using demo mode with simulated responses.';
                        statusNotice.className = statusNotice.className.replace('pink-500', 'yellow-500');
                    }
                } catch (error) {
                    statusIcon.className = 'fas fa-times-circle text-red-400 text-xl';
                    statusTitle.textContent = 'Connection Error';
                    statusMessage.textContent = 'Unable to connect to booking system. Please refresh the page.';
                    statusNotice.className = statusNotice.className.replace('pink-500', 'red-500');
                }
            }

            init() {
                this.loadServices();
                this.setupEventListeners();
            }

            setupEventListeners() {
                // Book service button
                const bookBtn = document.getElementById('book-service-btn');
                if (bookBtn) {
                    bookBtn.addEventListener('click', () => this.showBookingInterface());
                }

                // Navigation buttons
                const nextBtn = document.getElementById('next-step');
                const prevBtn = document.getElementById('prev-step');
                const confirmBtn = document.getElementById('confirm-booking');

                if (nextBtn) nextBtn.addEventListener('click', () => this.nextStep());
                if (prevBtn) prevBtn.addEventListener('click', () => this.prevStep());
                if (confirmBtn) confirmBtn.addEventListener('click', () => this.confirmBooking());
            }

            async loadServices() {
                const servicesGrid = document.getElementById('services-grid');
                servicesGrid.innerHTML = '<div class="text-center py-8 col-span-full"><div class="animate-spin inline-block w-8 h-8 border-4 border-pink-400 border-t-transparent rounded-full mb-4"></div><p class="text-gray-600">Loading braiding services...</p></div>';

                try {
                    const squareServices = await this.squareAPI.getServices();

                    // Filter for braiding-related services and transform to our format
                    this.availableServices = squareServices
                        .filter(item => item.type === 'ITEM' && item.item_data?.variations)
                        .filter(item => this.isBraidingService(item.item_data.name))
                        .map(item => {
                            const variation = item.item_data.variations[0];
                            const basePrice = variation.item_variation_data?.price_money?.amount || 0;

                            return {
                                id: item.id,
                                squareId: item.id,
                                variationId: variation.id,
                                name: item.item_data.name || 'Service',
                                description: item.item_data.description || 'Professional braiding service',
                                duration: this.extractDuration(item.item_data.name) || 180,
                                price: Math.round(basePrice / 100),
                                image: this.getServiceImage(item.item_data.name),
                                squareData: item
                            };
                        });

                    if (this.availableServices.length === 0) {
                        console.warn('No braiding services found in Square, using fallback services');
                        this.availableServices = this.getFallbackBraidingServices();
                    }

                    this.renderServices();
                } catch (error) {
                    console.error('Error loading services from Square:', error);
                    this.availableServices = this.getFallbackBraidingServices();
                    this.renderServices();
                    this.showError('Using offline services. Some features may be limited.');
                }
            }

            isBraidingService(serviceName) {
                const name = serviceName.toLowerCase();
                const braidingKeywords = ['braid', 'braids', 'cornrow', 'twist', 'protective', 'box', 'feed', 'goddess', 'fulani'];
                return braidingKeywords.some(keyword => name.includes(keyword));
            }

            getFallbackBraidingServices() {
                return [
                    {
                        id: 'box-braids-twists',
                        name: 'Box Braids & Twists',
                        description: 'Classic protective styling with modern techniques. Knotless options available for comfort and longevity',
                        duration: 240,
                        price: 200,
                        image: '../assets/images/box-braids.jpg'
                    },
                    {
                        id: 'cornrows-feedins',
                        name: 'Cornrows & Feed-ins',
                        description: 'Intricate patterns and designs that showcase artistry while protecting your natural hair',
                        duration: 180,
                        price: 120,
                        image: '../assets/images/cornrows.jpg'
                    },
                    {
                        id: 'protective-styling',
                        name: 'Protective Styling',
                        description: 'Custom protective styles including goddess braids, fulani braids, and creative updos',
                        duration: 210,
                        price: 150,
                        image: '../assets/images/protective-styling.jpg'
                    },
                    {
                        id: 'knotless-braids',
                        name: 'Knotless Braids',
                        description: 'Gentle, tension-free braiding technique for maximum comfort and natural appearance',
                        duration: 300,
                        price: 250,
                        image: '../assets/images/knotless-braids.jpg'
                    }
                ];
            }

            extractDuration(serviceName) {
                const name = serviceName.toLowerCase();
                if (name.includes('consultation')) return 30;
                if (name.includes('cornrow') && !name.includes('full')) return 180;
                if (name.includes('box') || name.includes('twist')) return 240;
                if (name.includes('knotless')) return 300;
                if (name.includes('goddess') || name.includes('fulani')) return 210;
                if (name.includes('protective')) return 210;
                return 180;
            }

            getServiceImage(serviceName) {
                const name = serviceName.toLowerCase();
                if (name.includes('box') || name.includes('twist')) return '../assets/images/box-braids.jpg';
                if (name.includes('cornrow') || name.includes('feed')) return '../assets/images/cornrows.jpg';
                if (name.includes('knotless')) return '../assets/images/knotless-braids.jpg';
                if (name.includes('protective') || name.includes('goddess') || name.includes('fulani')) return '../assets/images/protective-styling.jpg';
                return '../assets/images/default-service.jpg';
            }

            renderServices() {
                const servicesGrid = document.getElementById('services-grid');
                servicesGrid.innerHTML = this.availableServices.map(service => `
                    <div class="service-card glass-light rounded-2xl p-8 text-center cursor-pointer hover:shadow-lg transition-all" data-service-id="${service.id}">
                        <div class="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <span class="text-2xl">${this.getServiceEmoji(service.name)}</span>
                        </div>
                        <h3 class="font-accent text-2xl font-bold mb-4 text-gray-800">${service.name}</h3>
                        <p class="text-gray-600 mb-6">${service.description}</p>
                        <div class="text-pink-600 font-bold text-xl mb-4">$${service.price}</div>
                        <div class="text-gray-500 text-sm mb-4">${service.duration} minutes</div>
                        <button class="btn bg-pink-600 text-white hover:bg-pink-700 w-full py-3 rounded-lg select-service-btn">
                            <i class="fas fa-calendar-plus mr-2"></i>Select Service
                        </button>
                    </div>
                `).join('');

                // Add click handlers for service selection
                document.querySelectorAll('.service-card').forEach(card => {
                    card.addEventListener('click', () => this.selectServiceForBooking(card.dataset.serviceId));
                });

                // Enable book button
                const bookBtn = document.getElementById('book-service-btn');
                if (bookBtn) {
                    bookBtn.disabled = false;
                    bookBtn.textContent = 'Book Appointment';
                }
            }

            getServiceEmoji(serviceName) {
                const name = serviceName.toLowerCase();
                if (name.includes('box')) return '📦';
                if (name.includes('cornrow') || name.includes('feed')) return '🌽';
                if (name.includes('knotless')) return '🔗';
                if (name.includes('protective') || name.includes('goddess') || name.includes('fulani')) return '🛡️';
                return '💇‍♀️';
            }

            selectServiceForBooking(serviceId) {
                this.selectedService = this.availableServices.find(s => s.id === serviceId);
                this.showBookingInterface();
            }

            showBookingInterface() {
                const bookingSection = document.getElementById('booking-interface');
                if (bookingSection) {
                    bookingSection.style.display = 'block';
                    bookingSection.scrollIntoView({ behavior: 'smooth' });
                    this.loadBookingServices();
                }
            }

            loadBookingServices() {
                const bookingGrid = document.getElementById('booking-services-grid');
                if (!bookingGrid) return;

                bookingGrid.innerHTML = this.availableServices.map(service => `
                    <div class="booking-service-card ${this.selectedService?.id === service.id ? 'selected' : ''}" data-service-id="${service.id}">
                        <h4 class="text-lg font-bold text-gray-800 mb-2">${service.name}</h4>
                        <p class="text-gray-600 text-sm mb-3">${service.description}</p>
                        <div class="flex justify-between items-center">
                            <span class="text-pink-600 font-bold">$${service.price}</span>
                            <span class="text-gray-500 text-sm">${service.duration} min</span>
                        </div>
                    </div>
                `).join('');

                // Add click handlers
                document.querySelectorAll('.booking-service-card').forEach(card => {
                    card.addEventListener('click', () => this.selectService(card.dataset.serviceId));
                });

                // Enable next button if service is selected
                const nextBtn = document.getElementById('next-step');
                if (nextBtn && this.selectedService) {
                    nextBtn.disabled = false;
                }
            }

            selectService(serviceId) {
                document.querySelectorAll('.booking-service-card').forEach(card => {
                    card.classList.remove('selected');
                });

                const selectedCard = document.querySelector(`[data-service-id="${serviceId}"]`);
                selectedCard.classList.add('selected');

                this.selectedService = this.availableServices.find(s => s.id === serviceId);

                const nextBtn = document.getElementById('next-step');
                if (nextBtn) nextBtn.disabled = false;
            }

            showError(message) {
                console.error(message);
            }
        }

        // Hero Carousel Controller
        class HeroCarousel {
            constructor() {
                this.currentSlide = 1;
                this.totalSlides = 3;
                this.autoPlayInterval = null;
                this.autoPlayDelay = 6000; // 6 seconds per slide
                this.isAutoPlaying = true;

                this.init();
            }

            init() {
                this.setupEventListeners();
                this.startAutoPlay();
            }

            setupEventListeners() {
                // Navigation dots
                document.querySelectorAll('.nav-dot').forEach(dot => {
                    dot.addEventListener('click', (e) => {
                        const slideNumber = parseInt(e.target.dataset.slide);
                        this.goToSlide(slideNumber);
                        this.resetAutoPlay();
                    });
                });

                // Arrow navigation
                document.querySelector('.carousel-prev')?.addEventListener('click', () => {
                    this.prevSlide();
                    this.resetAutoPlay();
                });

                document.querySelector('.carousel-next')?.addEventListener('click', () => {
                    this.nextSlide();
                    this.resetAutoPlay();
                });

                // Pause auto-play on hover
                const carouselContainer = document.querySelector('.hero-carousel-container');
                carouselContainer?.addEventListener('mouseenter', () => this.pauseAutoPlay());
                carouselContainer?.addEventListener('mouseleave', () => this.resumeAutoPlay());

                // Keyboard navigation
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'ArrowLeft') {
                        this.prevSlide();
                        this.resetAutoPlay();
                    } else if (e.key === 'ArrowRight') {
                        this.nextSlide();
                        this.resetAutoPlay();
                    }
                });
            }

            goToSlide(slideNumber) {
                if (slideNumber < 1 || slideNumber > this.totalSlides) return;

                // Update slides
                document.querySelectorAll('.hero-slide').forEach(slide => {
                    slide.classList.remove('active', 'prev');
                    slide.style.zIndex = '1'; // Ensure slides are properly layered
                });

                const targetSlide = document.querySelector(`[data-slide="${slideNumber}"]`);
                targetSlide?.classList.add('active');
                targetSlide.style.zIndex = '2'; // Bring active slide to the front

                // Update navigation dots
                document.querySelectorAll('.nav-dot').forEach(dot => {
                    dot.classList.remove('active');
                });

                const targetDot = document.querySelector(`.nav-dot[data-slide="${slideNumber}"]`);
                targetDot?.classList.add('active');

                this.currentSlide = slideNumber;
            }

            nextSlide() {
                const nextSlide = this.currentSlide >= this.totalSlides ? 1 : this.currentSlide + 1;
                this.goToSlide(nextSlide);
            }

            prevSlide() {
                const prevSlide = this.currentSlide <= 1 ? this.totalSlides : this.currentSlide - 1;
                this.goToSlide(prevSlide);
            }

            startAutoPlay() {
                if (!this.isAutoPlaying) return;

                this.autoPlayInterval = setInterval(() => {
                    this.nextSlide();
                }, this.autoPlayDelay);
            }

            pauseAutoPlay() {
                if (this.autoPlayInterval) {
                    clearInterval(this.autoPlayInterval);
                    this.autoPlayInterval = null;
                }
            }

            resumeAutoPlay() {
                if (this.isAutoPlaying && !this.autoPlayInterval) {
                    this.startAutoPlay();
                }
            }

            resetAutoPlay() {
                this.pauseAutoPlay();
                this.resumeAutoPlay();
            }

            scrollToBooking() {
                const bookingSection = document.getElementById('booking-interface');
                if (bookingSection) {
                    bookingSection.style.display = 'block';
                    bookingSection.scrollIntoView({ behavior: 'smooth' });
                } else {
                    // Fallback to services section
                    const servicesSection = document.getElementById('services');
                    servicesSection?.scrollIntoView({ behavior: 'smooth' });
                }
            }
        }

        // Initialize the booking interface and carousel when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            new BraidingBookingInterface();
            new HeroCarousel();
        });
    </script>

    <!-- Square Booking Integration -->
    <script src="../assets/js/square-booking.js"></script>
</body>
</html>
