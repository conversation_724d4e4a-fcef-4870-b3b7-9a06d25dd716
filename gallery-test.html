<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gallery Test - GetTwisted Studios</title>
    
    <!-- Production CSS -->
    <link rel="stylesheet" href="assets/css/tailwind-production.css">
    <link rel="stylesheet" href="assets/css/futuristic.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            font-family: 'Exo 2', sans-serif;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-pass { background-color: #10B981; }
        .status-fail { background-color: #EF4444; }
        .status-pending { background-color: #F59E0B; }
        
        .test-result {
            margin: 1rem 0;
            padding: 0.5rem;
            border-radius: 0.5rem;
            background: rgba(0, 0, 0, 0.2);
        }
        
        button {
            background: linear-gradient(45deg, #8B5CF6, #06B6D4);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            color: white;
            cursor: pointer;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }
        
        button:hover {
            transform: scale(1.05);
            box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-4xl font-bold text-center mb-8">
            <i class="fas fa-vial text-neon-cyan"></i>
            Gallery System Test Suite
        </h1>
        
        <div class="test-section">
            <h2 class="text-2xl font-bold mb-4">
                <i class="fas fa-cog"></i> System Status
            </h2>
            
            <div id="css-test" class="test-result">
                <span class="status-indicator status-pending"></span>
                <strong>CSS Loading:</strong> <span id="css-status">Testing...</span>
            </div>
            
            <div id="js-test" class="test-result">
                <span class="status-indicator status-pending"></span>
                <strong>JavaScript:</strong> <span id="js-status">Testing...</span>
            </div>
            
            <div id="data-test" class="test-result">
                <span class="status-indicator status-pending"></span>
                <strong>Data Loading:</strong> <span id="data-status">Testing...</span>
            </div>
            
            <div id="csp-test" class="test-result">
                <span class="status-indicator status-pending"></span>
                <strong>CSP Compliance:</strong> <span id="csp-status">Testing...</span>
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="text-2xl font-bold mb-4">
                <i class="fas fa-play"></i> Manual Tests
            </h2>
            
            <button onclick="testDataFetch()">Test Data Fetch</button>
            <button onclick="testPortfolioManager()">Test Portfolio Manager</button>
            <button onclick="testGalleryManager()">Test Gallery Manager</button>
            <button onclick="window.open('gallery.html', '_blank')">Open Gallery</button>
            
            <div id="manual-results" class="mt-4"></div>
        </div>
        
        <div class="test-section">
            <h2 class="text-2xl font-bold mb-4">
                <i class="fas fa-info-circle"></i> Production Readiness
            </h2>
            
            <div id="production-checklist">
                <div class="test-result">
                    <span class="status-indicator status-pass"></span>
                    <strong>✓ CSP Headers Updated:</strong> Added localhost and Google Analytics domains
                </div>
                <div class="test-result">
                    <span class="status-indicator status-pass"></span>
                    <strong>✓ JavaScript Syntax Fixed:</strong> Removed orphaned code and proper structure
                </div>
                <div class="test-result">
                    <span class="status-indicator status-pass"></span>
                    <strong>✓ Production CSS:</strong> Replaced CDN with local production build
                </div>
                <div class="test-result">
                    <span class="status-indicator status-pass"></span>
                    <strong>✓ Data Files:</strong> JSON files accessible for gallery content
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test CSS Loading
        function testCSS() {
            const testElement = document.createElement('div');
            testElement.className = 'hidden';
            document.body.appendChild(testElement);
            
            const isHidden = window.getComputedStyle(testElement).display === 'none';
            document.body.removeChild(testElement);
            
            updateStatus('css', isHidden, isHidden ? 'Production CSS loaded successfully' : 'CSS loading failed');
        }
        
        // Test JavaScript
        function testJS() {
            try {
                const testObj = { test: true };
                const result = JSON.stringify(testObj);
                updateStatus('js', true, 'JavaScript engine working correctly');
            } catch (error) {
                updateStatus('js', false, `JavaScript error: ${error.message}`);
            }
        }
        
        // Test Data Loading
        async function testDataFetch() {
            try {
                const response = await fetch('assets/data/gallery-images.json');
                if (response.ok) {
                    const data = await response.json();
                    updateStatus('data', true, `Data loaded: ${Object.keys(data.services || {}).length} services found`);
                    addManualResult('✓ Gallery data fetch successful', 'pass');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                updateStatus('data', false, `Data loading failed: ${error.message}`);
                addManualResult('✗ Gallery data fetch failed: ' + error.message, 'fail');
            }
        }
        
        // Test CSP
        function testCSP() {
            // Check if we can make requests (CSP allows it)
            try {
                fetch('assets/data/gallery-images.json')
                    .then(() => updateStatus('csp', true, 'CSP allows necessary connections'))
                    .catch(() => updateStatus('csp', false, 'CSP blocking connections'));
            } catch (error) {
                updateStatus('csp', false, 'CSP test failed');
            }
        }
        
        // Test Portfolio Manager
        function testPortfolioManager() {
            try {
                if (typeof StylistPortfolioManager !== 'undefined') {
                    addManualResult('✓ StylistPortfolioManager class available', 'pass');
                } else {
                    addManualResult('⚠ StylistPortfolioManager not loaded (may load on gallery page)', 'pending');
                }
            } catch (error) {
                addManualResult('✗ Portfolio Manager test failed: ' + error.message, 'fail');
            }
        }
        
        // Test Gallery Manager
        function testGalleryManager() {
            try {
                if (typeof FuturisticGallery !== 'undefined') {
                    addManualResult('✓ FuturisticGallery class available', 'pass');
                } else {
                    addManualResult('⚠ FuturisticGallery not loaded (may load on gallery page)', 'pending');
                }
            } catch (error) {
                addManualResult('✗ Gallery Manager test failed: ' + error.message, 'fail');
            }
        }
        
        // Helper functions
        function updateStatus(test, passed, message) {
            const statusElement = document.getElementById(`${test}-status`);
            const indicatorElement = document.querySelector(`#${test}-test .status-indicator`);
            
            statusElement.textContent = message;
            indicatorElement.className = `status-indicator ${passed ? 'status-pass' : 'status-fail'}`;
        }
        
        function addManualResult(message, status) {
            const resultsContainer = document.getElementById('manual-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-result';
            resultDiv.innerHTML = `
                <span class="status-indicator status-${status}"></span>
                ${message}
            `;
            resultsContainer.appendChild(resultDiv);
        }
        
        // Run automatic tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testCSS();
                testJS();
                testDataFetch();
                testCSP();
            }, 500);
        });
    </script>
</body>
</html>
