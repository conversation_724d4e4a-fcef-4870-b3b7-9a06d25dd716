<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Our Stylists - GetTwisted Hair Studios | Meet Our Expert Team</title>
    <meta name="description" content="Meet the talented stylists at GetTwisted Hair Studios. Expert locticians, braiders, and barbers specializing in natural hair care and innovative styling.">
    <meta name="keywords" content="hair stylists, locticians, braiders, barbers, natural hair specialists, Philadelphia hair team, GetTwisted">
    
    <!-- Open Graph Meta -->
    <meta property="og:title" content="Our Stylists - GetTwisted Hair Studios">
    <meta property="og:description" content="Meet our team of expert hair professionals specializing in locs, braids, and barbering.">
    <meta property="og:image" content="https://gettwistedlocs.com/assets/common/hero.jpg">
    <meta property="og:url" content="https://gettwistedlocs.com/stylists.html">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;600;700&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Design System -->
    <link rel="stylesheet" href="assets/css/futuristic.css">
    
    <!-- Custom Styles -->
    <style>
        .stylists-hero-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            overflow: hidden;
        }
        
        .stylists-hero-gradient::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(139, 92, 246, 0.3), rgba(6, 182, 212, 0.3));
            z-index: 1;
        }
        
        .stylists-hero-gradient > * {
            position: relative;
            z-index: 2;
        }
        
        .stylist-card {
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .stylist-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(139, 92, 246, 0.3);
        }
        
        .specialty-badge {
            background: linear-gradient(45deg, rgba(139, 92, 246, 0.8), rgba(6, 182, 212, 0.8));
            backdrop-filter: blur(10px);
        }
        
        .social-link {
            transition: all 0.3s ease;
        }
        
        .social-link:hover {
            transform: scale(1.2);
        }
        
        .portfolio-modal {
            backdrop-filter: blur(20px);
            background: rgba(0, 0, 0, 0.8);
        }
        
        .modal-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>

<body class="bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 min-h-screen text-white">
    <!-- Futuristic Navigation -->
    <nav class="glass-enhanced fixed top-0 left-0 right-0 z-50 px-6 py-4">
        <div class="max-w-7xl mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <a href="index.html" class="font-display text-2xl text-white hover:text-purple-300 transition-smooth">
                    <img src="assets/common/gettwisted-logo.png" alt="Get Twisted Hair Studio Logo" class="logo">
                </a>
                <span class="text-purple-300">|</span>
                <span class="font-accent text-lg text-purple-200">Our Team</span>
            </div>
            <div class="hidden md:flex space-x-8">
                <a href="index.html" class="text-white hover:text-purple-300 transition-smooth">Home</a>
                <a href="stylists.html" class="text-purple-300 font-semibold">Stylists</a>
                <a href="gallery.html" class="text-white hover:text-purple-300 transition-smooth">Gallery</a>
                <a href="careers.html" class="text-white hover:text-purple-300 transition-smooth">Careers</a>
                <a href="book-appointment.html" class="btn-holographic px-4 py-2 rounded-lg text-sm">Book Now</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="stylists-hero-gradient min-h-screen flex items-center justify-center pt-20">
        <div class="container mx-auto px-4 text-center">
            <div class="animate-fade-in-up">
                <div class="text-6xl mb-6 floating-element">✨</div>
                <h1 class="font-display text-5xl md:text-7xl text-white mb-6">
                    Meet Our <span class="text-neon-cyan">Expert</span> Team
                </h1>
                <p class="font-accent text-xl md:text-2xl text-white mb-8 max-w-3xl mx-auto">
                    Talented artists passionate about natural hair care, innovative styling, and creating stunning transformations.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="#team" class="btn-holographic px-8 py-4 rounded-lg">
                        <i class="fas fa-users mr-2"></i>Meet the Team
                    </a>
                    <a href="book-appointment.html" class="btn btn-lg glass text-white hover:bg-white hover:text-purple-600">
                        <i class="fas fa-calendar mr-2"></i>Book Appointment
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Stats Section -->
    <section class="section-padding bg-gradient-to-r from-purple-900/50 to-blue-900/50">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
                <div class="glass-enhanced rounded-2xl p-8">
                    <div class="text-4xl font-bold text-neon-purple mb-2">8+</div>
                    <div class="text-gray-300">Expert Stylists</div>
                </div>
                <div class="glass-enhanced rounded-2xl p-8">
                    <div class="text-4xl font-bold text-neon-cyan mb-2">15+</div>
                    <div class="text-gray-300">Years Combined Experience</div>
                </div>
                <div class="glass-enhanced rounded-2xl p-8">
                    <div class="text-4xl font-bold text-neon-pink mb-2">1000+</div>
                    <div class="text-gray-300">Happy Clients</div>
                </div>
                <div class="glass-enhanced rounded-2xl p-8">
                    <div class="text-4xl font-bold text-neon-green mb-2">3</div>
                    <div class="text-gray-300">Specialty Studios</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Section -->
    <section id="team" class="section-padding bg-neutral-100">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="font-accent text-5xl font-bold mb-6 text-gray-800">Our Talented Team</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Each stylist brings unique expertise and passion to create the perfect look for every client.
                </p>
            </div>
            
            <!-- Locs Specialists -->
            <div class="mb-16">
                <h3 class="font-accent text-3xl font-bold mb-8 text-center text-purple-600">Locs Specialists</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Stylist 1 -->
                    <div class="stylist-card glass-light rounded-2xl p-6 bg-white border border-purple-200">
                        <div class="relative mb-6">
                            <img src="assets/stylists/maya-johnson.jpg" alt="Maya Johnson" class="w-full h-64 object-cover rounded-xl">
                            <div class="specialty-badge absolute top-4 left-4 px-3 py-1 rounded-full text-white text-sm font-bold">
                                Master Loctician
                            </div>
                        </div>
                        <h4 class="font-accent text-2xl font-bold text-gray-800 mb-2">Maya Johnson</h4>
                        <p class="text-purple-600 font-medium mb-4">5+ Years Experience</p>
                        <p class="text-gray-600 mb-4">
                            Specializes in starter locs, retwists, and loc maintenance. Known for her gentle touch and educational approach.
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="px-3 py-1 bg-purple-100 text-purple-600 rounded-full text-sm">Starter Locs</span>
                            <span class="px-3 py-1 bg-purple-100 text-purple-600 rounded-full text-sm">Retwists</span>
                            <span class="px-3 py-1 bg-purple-100 text-purple-600 rounded-full text-sm">Loc Styling</span>
                        </div>
                        <div class="flex space-x-4">
                            <a href="#" class="social-link text-purple-600 hover:text-purple-800">
                                <i class="fab fa-instagram text-xl"></i>
                            </a>
                            <button onclick="openPortfolio('Maya Johnson')" class="btn-holographic px-4 py-2 rounded-lg text-sm">
                                View Portfolio
                            </button>
                        </div>
                    </div>
                    
                    <!-- Stylist 2 -->
                    <div class="stylist-card glass-light rounded-2xl p-6 bg-white border border-purple-200">
                        <div class="relative mb-6">
                            <img src="assets/stylists/jordan-williams.jpg" alt="Jordan Williams" class="w-full h-64 object-cover rounded-xl">
                            <div class="specialty-badge absolute top-4 left-4 px-3 py-1 rounded-full text-white text-sm font-bold">
                                Loc Artist
                            </div>
                        </div>
                        <h4 class="font-accent text-2xl font-bold text-gray-800 mb-2">Jordan Williams</h4>
                        <p class="text-purple-600 font-medium mb-4">3+ Years Experience</p>
                        <p class="text-gray-600 mb-4">
                            Creative loc styling and color specialist. Passionate about helping clients express their unique style.
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="px-3 py-1 bg-purple-100 text-purple-600 rounded-full text-sm">Loc Color</span>
                            <span class="px-3 py-1 bg-purple-100 text-purple-600 rounded-full text-sm">Creative Styling</span>
                            <span class="px-3 py-1 bg-purple-100 text-purple-600 rounded-full text-sm">Maintenance</span>
                        </div>
                        <div class="flex space-x-4">
                            <a href="#" class="social-link text-purple-600 hover:text-purple-800">
                                <i class="fab fa-instagram text-xl"></i>
                            </a>
                            <button onclick="openPortfolio('Jordan Williams')" class="btn-holographic px-4 py-2 rounded-lg text-sm">
                                View Portfolio
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Braiding Specialists -->
            <div class="mb-16">
                <h3 class="font-accent text-3xl font-bold mb-8 text-center text-pink-600">Braiding Specialists</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Braider 1 -->
                    <div class="stylist-card glass-light rounded-2xl p-6 bg-white border border-pink-200">
                        <div class="relative mb-6">
                            <img src="assets/stylists/zara-thompson.jpg" alt="Zara Thompson" class="w-full h-64 object-cover rounded-xl">
                            <div class="specialty-badge absolute top-4 left-4 px-3 py-1 rounded-full text-white text-sm font-bold">
                                Master Braider
                            </div>
                        </div>
                        <h4 class="font-accent text-2xl font-bold text-gray-800 mb-2">Zara Thompson</h4>
                        <p class="text-pink-600 font-medium mb-4">6+ Years Experience</p>
                        <p class="text-gray-600 mb-4">
                            Expert in knotless braids, box braids, and protective styling. Known for speed and precision.
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="px-3 py-1 bg-pink-100 text-pink-600 rounded-full text-sm">Knotless Braids</span>
                            <span class="px-3 py-1 bg-pink-100 text-pink-600 rounded-full text-sm">Box Braids</span>
                            <span class="px-3 py-1 bg-pink-100 text-pink-600 rounded-full text-sm">Cornrows</span>
                        </div>
                        <div class="flex space-x-4">
                            <a href="#" class="social-link text-pink-600 hover:text-pink-800">
                                <i class="fab fa-instagram text-xl"></i>
                            </a>
                            <button onclick="openPortfolio('Zara Thompson')" class="btn-holographic px-4 py-2 rounded-lg text-sm">
                                View Portfolio
                            </button>
                        </div>
                    </div>

                    <!-- Braider 2 -->
                    <div class="stylist-card glass-light rounded-2xl p-6 bg-white border border-pink-200">
                        <div class="relative mb-6">
                            <img src="assets/stylists/amara-davis.jpg" alt="Amara Davis" class="w-full h-64 object-cover rounded-xl">
                            <div class="specialty-badge absolute top-4 left-4 px-3 py-1 rounded-full text-white text-sm font-bold">
                                Creative Braider
                            </div>
                        </div>
                        <h4 class="font-accent text-2xl font-bold text-gray-800 mb-2">Amara Davis</h4>
                        <p class="text-pink-600 font-medium mb-4">4+ Years Experience</p>
                        <p class="text-gray-600 mb-4">
                            Specializes in creative patterns and intricate designs. Perfect for clients wanting unique styles.
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="px-3 py-1 bg-pink-100 text-pink-600 rounded-full text-sm">Creative Patterns</span>
                            <span class="px-3 py-1 bg-pink-100 text-pink-600 rounded-full text-sm">Tribal Braids</span>
                            <span class="px-3 py-1 bg-pink-100 text-pink-600 rounded-full text-sm">Goddess Braids</span>
                        </div>
                        <div class="flex space-x-4">
                            <a href="#" class="social-link text-pink-600 hover:text-pink-800">
                                <i class="fab fa-instagram text-xl"></i>
                            </a>
                            <button onclick="openPortfolio('Amara Davis')" class="btn-holographic px-4 py-2 rounded-lg text-sm">
                                View Portfolio
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Barbers -->
            <div class="mb-16">
                <h3 class="font-accent text-3xl font-bold mb-8 text-center text-green-600">Master Barbers</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Barber 1 -->
                    <div class="stylist-card glass-light rounded-2xl p-6 bg-white border border-green-200">
                        <div class="relative mb-6">
                            <img src="assets/stylists/marcus-brown.jpg" alt="Marcus Brown" class="w-full h-64 object-cover rounded-xl">
                            <div class="specialty-badge absolute top-4 left-4 px-3 py-1 rounded-full text-white text-sm font-bold">
                                Master Barber
                            </div>
                        </div>
                        <h4 class="font-accent text-2xl font-bold text-gray-800 mb-2">Marcus Brown</h4>
                        <p class="text-green-600 font-medium mb-4">8+ Years Experience</p>
                        <p class="text-gray-600 mb-4">
                            Precision cuts, fades, and beard grooming specialist. Known for attention to detail and classic techniques.
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="px-3 py-1 bg-green-100 text-green-600 rounded-full text-sm">Fades</span>
                            <span class="px-3 py-1 bg-green-100 text-green-600 rounded-full text-sm">Beard Grooming</span>
                            <span class="px-3 py-1 bg-green-100 text-green-600 rounded-full text-sm">Hot Towel</span>
                        </div>
                        <div class="flex space-x-4">
                            <a href="#" class="social-link text-green-600 hover:text-green-800">
                                <i class="fab fa-instagram text-xl"></i>
                            </a>
                            <button onclick="openPortfolio('Marcus Brown')" class="btn-holographic px-4 py-2 rounded-lg text-sm">
                                View Portfolio
                            </button>
                        </div>
                    </div>

                    <!-- Barber 2 -->
                    <div class="stylist-card glass-light rounded-2xl p-6 bg-white border border-green-200">
                        <div class="relative mb-6">
                            <img src="assets/stylists/devon-clark.jpg" alt="Devon Clark" class="w-full h-64 object-cover rounded-xl">
                            <div class="specialty-badge absolute top-4 left-4 px-3 py-1 rounded-full text-white text-sm font-bold">
                                Creative Barber
                            </div>
                        </div>
                        <h4 class="font-accent text-2xl font-bold text-gray-800 mb-2">Devon Clark</h4>
                        <p class="text-green-600 font-medium mb-4">5+ Years Experience</p>
                        <p class="text-gray-600 mb-4">
                            Modern cuts and creative designs. Specializes in contemporary styles and artistic hair designs.
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="px-3 py-1 bg-green-100 text-green-600 rounded-full text-sm">Modern Cuts</span>
                            <span class="px-3 py-1 bg-green-100 text-green-600 rounded-full text-sm">Hair Designs</span>
                            <span class="px-3 py-1 bg-green-100 text-green-600 rounded-full text-sm">Tapers</span>
                        </div>
                        <div class="flex space-x-4">
                            <a href="#" class="social-link text-green-600 hover:text-green-800">
                                <i class="fab fa-instagram text-xl"></i>
                            </a>
                            <button onclick="openPortfolio('Devon Clark')" class="btn-holographic px-4 py-2 rounded-lg text-sm">
                                View Portfolio
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Modal -->
    <div id="portfolioModal" class="portfolio-modal fixed inset-0 z-50 hidden flex items-center justify-center p-4">
        <div class="modal-content rounded-2xl p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div class="flex items-center justify-between mb-6">
                <h3 class="font-accent text-2xl font-bold text-white" id="portfolioTitle">Stylist Portfolio</h3>
                <button onclick="closePortfolio()" class="text-white hover:text-red-400 text-2xl">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div id="portfolioContent" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Portfolio images will be loaded here -->
            </div>

            <div class="mt-8 text-center">
                <a href="book-appointment.html" class="btn-holographic px-8 py-3 rounded-lg">
                    <i class="fas fa-calendar mr-2"></i>Book with This Stylist
                </a>
            </div>
        </div>
    </div>

    <!-- Call to Action Section -->
    <section class="section-padding bg-gradient-to-r from-purple-900 to-indigo-900">
        <div class="container mx-auto px-4 text-center">
            <h2 class="font-accent text-4xl font-bold mb-6 text-white">Ready to Transform Your Look?</h2>
            <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                Book an appointment with one of our expert stylists and experience the GetTwisted difference.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="book-appointment.html" class="btn-holographic px-8 py-4 rounded-lg">
                    <i class="fas fa-calendar mr-2"></i>Book Appointment
                </a>
                <a href="gallery.html" class="btn btn-lg glass text-white hover:bg-white hover:text-purple-600">
                    <i class="fas fa-images mr-2"></i>View Gallery
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gradient-to-r from-purple-900 to-indigo-900 text-white py-16">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="font-display text-2xl mb-4">GetTwisted</h3>
                    <p class="text-gray-300 mb-4">Expert stylists specializing in locs, braids, and barbering with a futuristic approach.</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-purple-300 hover:text-white"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-purple-300 hover:text-white"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-purple-300 hover:text-white"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>

                <div>
                    <h4 class="font-accent text-lg font-bold mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="index.html" class="text-gray-300 hover:text-white">Home</a></li>
                        <li><a href="stylists.html" class="text-gray-300 hover:text-white">Stylists</a></li>
                        <li><a href="gallery.html" class="text-gray-300 hover:text-white">Gallery</a></li>
                        <li><a href="careers.html" class="text-gray-300 hover:text-white">Careers</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="font-accent text-lg font-bold mb-4">Services</h4>
                    <ul class="space-y-2">
                        <li><a href="locs/index.html" class="text-gray-300 hover:text-white">Locs</a></li>
                        <li><a href="braids/index.html" class="text-gray-300 hover:text-white">Braids</a></li>
                        <li><a href="barber/index.html" class="text-gray-300 hover:text-white">Barbering</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="font-accent text-lg font-bold mb-4">Contact</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li><i class="fas fa-phone mr-2"></i>(*************</li>
                        <li><i class="fas fa-envelope mr-2"></i><EMAIL></li>
                        <li><i class="fas fa-map-marker-alt mr-2"></i>Philadelphia, PA</li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-purple-700 mt-12 pt-8 text-center">
                <p class="text-gray-300">&copy; 2024 GetTwisted Hair Studios. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Portfolio data for each stylist
        const portfolioData = {
            'Maya Johnson': [
                'assets/gallery/locs/maya-1.jpg',
                'assets/gallery/locs/maya-2.jpg',
                'assets/gallery/locs/maya-3.jpg',
                'assets/gallery/locs/maya-4.jpg'
            ],
            'Jordan Williams': [
                'assets/gallery/locs/jordan-1.jpg',
                'assets/gallery/locs/jordan-2.jpg',
                'assets/gallery/locs/jordan-3.jpg',
                'assets/gallery/locs/jordan-4.jpg'
            ],
            'Zara Thompson': [
                'assets/gallery/braids/zara-1.jpg',
                'assets/gallery/braids/zara-2.jpg',
                'assets/gallery/braids/zara-3.jpg',
                'assets/gallery/braids/zara-4.jpg'
            ],
            'Amara Davis': [
                'assets/gallery/braids/amara-1.jpg',
                'assets/gallery/braids/amara-2.jpg',
                'assets/gallery/braids/amara-3.jpg',
                'assets/gallery/braids/amara-4.jpg'
            ],
            'Marcus Brown': [
                'assets/gallery/barber/marcus-1.jpg',
                'assets/gallery/barber/marcus-2.jpg',
                'assets/gallery/barber/marcus-3.jpg',
                'assets/gallery/barber/marcus-4.jpg'
            ],
            'Devon Clark': [
                'assets/gallery/barber/devon-1.jpg',
                'assets/gallery/barber/devon-2.jpg',
                'assets/gallery/barber/devon-3.jpg',
                'assets/gallery/barber/devon-4.jpg'
            ]
        };

        // Portfolio Modal Functions
        function openPortfolio(stylistName) {
            document.getElementById('portfolioTitle').textContent = `${stylistName}'s Portfolio`;

            const portfolioContent = document.getElementById('portfolioContent');
            portfolioContent.innerHTML = '';

            const images = portfolioData[stylistName] || [];

            if (images.length === 0) {
                portfolioContent.innerHTML = `
                    <div class="col-span-full text-center text-white">
                        <i class="fas fa-images text-4xl mb-4 opacity-50"></i>
                        <p>Portfolio coming soon! Check back later to see ${stylistName}'s amazing work.</p>
                    </div>
                `;
            } else {
                images.forEach((imageSrc, index) => {
                    const imageDiv = document.createElement('div');
                    imageDiv.className = 'relative group cursor-pointer';
                    imageDiv.innerHTML = `
                        <img src="${imageSrc}" alt="${stylistName} work ${index + 1}"
                             class="w-full h-48 object-cover rounded-lg transition-transform group-hover:scale-105"
                             onerror="this.src='assets/common/placeholder.jpg'">
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all rounded-lg flex items-center justify-center">
                            <i class="fas fa-expand text-white opacity-0 group-hover:opacity-100 text-2xl"></i>
                        </div>
                    `;
                    portfolioContent.appendChild(imageDiv);
                });
            }

            document.getElementById('portfolioModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closePortfolio() {
            document.getElementById('portfolioModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Close modal when clicking outside
        document.getElementById('portfolioModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePortfolio();
            }
        });

        // Escape key to close modal
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closePortfolio();
            }
        });

        // Add floating animation to hero emoji
        document.addEventListener('DOMContentLoaded', function() {
            const floatingElement = document.querySelector('.floating-element');
            if (floatingElement) {
                setInterval(() => {
                    floatingElement.style.transform = `translateY(${Math.sin(Date.now() * 0.002) * 10}px)`;
                }, 16);
            }
        });
    </script>
</body>
</html>
