/**
 * Gallery JavaScript for GetTwisted Hair Studios - Root Directory Copy
 * This is a copy of js/gallery.js to resolve 404 errors when gallery.js is requested from root
 * 
 * IMPORTANT: This file should redirect to the main gallery system
 * The actual gallery functionality is handled by:
 * - assets/js/gallery-manager.js (main gallery system)
 * - assets/js/stylist-portfolio-manager.js (portfolio system)
 * - gallery.html (unified gallery page)
 */

console.log('⚠️ gallery.js loaded from root directory');
console.log('🔄 Redirecting to unified gallery system...');

// Check if we're on a page that should use the unified gallery
if (window.location.pathname.includes('gallery') || 
    document.getElementById('gallery-modal') || 
    document.getElementById('portfolio-container')) {
    
    console.log('✅ Gallery page detected - unified system should handle this');
    
    // If gallery manager is not loaded, provide fallback
    if (typeof window.galleryManager === 'undefined' && 
        typeof GalleryManager !== 'undefined') {
        try {
            window.galleryManager = new GalleryManager();
            console.log('🖼️ Fallback Gallery Manager initialized from root gallery.js');
        } catch (error) {
            console.warn('❌ Failed to initialize fallback gallery manager:', error);
        }
    }
    
    // If portfolio manager is not loaded, provide fallback
    if (typeof window.portfolioManager === 'undefined' && 
        typeof StylistPortfolioManager !== 'undefined') {
        try {
            const container = document.getElementById('portfolio-container');
            if (container) {
                window.portfolioManager = new StylistPortfolioManager('portfolio-container');
                console.log('👤 Fallback Portfolio Manager initialized from root gallery.js');
            }
        } catch (error) {
            console.warn('❌ Failed to initialize fallback portfolio manager:', error);
        }
    }
    
} else {
    // For other pages, provide minimal gallery functionality
    console.log('ℹ️ Non-gallery page detected - providing minimal functionality');
    
    // Provide basic gallery opening function if needed
    if (typeof window.openGalleryModal === 'undefined') {
        window.openGalleryModal = function(imageData = {}) {
            console.log('🔗 Redirecting to main gallery page...');
            window.location.href = '/gallery.html';
        };
    }
}

// Legacy support - if old gallery functions are called, redirect to new system
if (typeof window.openLightbox === 'undefined') {
    window.openLightbox = function(index) {
        console.log('🔗 Legacy openLightbox called - redirecting to gallery page');
        window.location.href = '/gallery.html';
    };
}

console.log('✅ Root gallery.js initialization complete');
