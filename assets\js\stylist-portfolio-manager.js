/**
 * Stylist Portfolio Management System
 * Manages stylist portfolios, work assignments, and filtering
 */

class StylistPortfolioManager {
    constructor() {
        this.portfolioData = null;
        this.currentStylist = null;
        this.currentCategory = 'all';
        this.currentView = 'grid'; // grid, list, carousel
        this.isLoading = false;
        this.cache = new Map();
        
        this.init();
    }

    async init() {
        console.log('🎨 Initializing Stylist Portfolio Manager...');
        try {
            await this.loadPortfolioData();
            this.setupEventListeners();
            console.log('✅ Stylist Portfolio Manager initialized successfully');
        } catch (error) {
            console.error('❌ Failed to initialize Stylist Portfolio Manager:', error);
            this.handleError(error);
        }
    }

    async loadPortfolioData() {
        try {
            const response = await fetch('assets/data/stylist-portfolios.json');
            if (!response.ok) throw new Error('Failed to load portfolio data');
            
            this.portfolioData = await response.json();
            console.log('📊 Portfolio data loaded:', Object.keys(this.portfolioData.stylists).length, 'stylists');
        } catch (error) {
            console.warn('⚠️ Using fallback portfolio data');
            this.portfolioData = this.getFallbackData();
        }
    }

    setupEventListeners() {
        // Stylist filter buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-stylist]')) {
                const stylistId = e.target.dataset.stylist;
                this.filterByStylist(stylistId);
            }
            
            if (e.target.matches('[data-category]')) {
                const category = e.target.dataset.category;
                this.filterByCategory(category);
            }
            
            if (e.target.matches('[data-view]')) {
                const view = e.target.dataset.view;
                this.changeView(view);
            }
        });

        // Portfolio modal events
        document.addEventListener('click', (e) => {
            if (e.target.matches('.portfolio-item') || e.target.closest('.portfolio-item')) {
                const portfolioItem = e.target.matches('.portfolio-item') ? e.target : e.target.closest('.portfolio-item');
                const workId = portfolioItem.dataset.workId;
                this.showWorkDetailInStaticModal(workId);
            }
            
            if (e.target.matches('.stylist-profile-btn')) {
                const stylistId = e.target.dataset.stylistId;
                this.showStylistProfile(stylistId);
            }
        });
    }

    // Filter portfolio by stylist
    filterByStylist(stylistId) {
        this.currentStylist = stylistId;
        this.updateActiveFilters();
        this.renderPortfolio();
        
        // Update URL without page reload
        const url = new URL(window.location);
        if (stylistId === 'all') {
            url.searchParams.delete('stylist');
        } else {
            url.searchParams.set('stylist', stylistId);
        }
        window.history.pushState({}, '', url);
    }

    // Filter portfolio by category
    filterByCategory(category) {
        this.currentCategory = category;
        this.updateActiveFilters();
        this.renderPortfolio();
        
        // Update URL
        const url = new URL(window.location);
        if (category === 'all') {
            url.searchParams.delete('category');
        } else {
            url.searchParams.set('category', category);
        }
        window.history.pushState({}, '', url);
    }

    // Change view mode (grid, list, carousel)
    changeView(view) {
        this.currentView = view;
        this.updateViewButtons();
        this.renderPortfolio();
    }

    // Get filtered portfolio items
    getFilteredPortfolio() {
        if (!this.portfolioData) return [];
        
        let allWork = [];
        
        // Collect all work from all stylists
        Object.values(this.portfolioData.stylists).forEach(stylist => {
            const featuredWork = stylist.portfolio.featured_work || [];
            const recentWork = stylist.portfolio.recent_work || [];
            
            [...featuredWork, ...recentWork].forEach(work => {
                allWork.push({
                    ...work,
                    stylist: stylist,
                    stylistId: stylist.id,
                    stylistName: stylist.name,
                    stylistDisplayName: stylist.displayName
                });
            });
        });

        // Apply filters
        let filtered = allWork;

        if (this.currentStylist && this.currentStylist !== 'all') {
            filtered = filtered.filter(work => work.stylistId === this.currentStylist);
        }

        if (this.currentCategory && this.currentCategory !== 'all') {
            filtered = filtered.filter(work => work.category === this.currentCategory);
        }

        // Sort by date (newest first)
        filtered.sort((a, b) => new Date(b.date) - new Date(a.date));

        return filtered;
    }

    // Render portfolio based on current filters and view
    renderPortfolio() {
        const container = document.getElementById('portfolio-container') || 
                         document.getElementById('gallery-grid') ||
                         document.querySelector('.gallery-grid');
        
        if (!container) {
            console.warn('⚠️ Portfolio container not found');
            return;
        }

        const filteredWork = this.getFilteredPortfolio();
        
        if (filteredWork.length === 0) {
            this.renderEmptyState(container);
            return;
        }

        switch (this.currentView) {
            case 'list':
                this.renderListView(container, filteredWork);
                break;
            case 'carousel':
                this.renderCarouselView(container, filteredWork);
                break;
            default:
                this.renderGridView(container, filteredWork);
        }

        this.updateResultsCount(filteredWork.length);
    }

    // Render grid view (default)
    renderGridView(container, work) {
        container.className = 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6';
        
        container.innerHTML = work.map(item => `
            <div class="portfolio-item glass rounded-xl overflow-hidden cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-2xl" 
                 data-work-id="${item.id}">
                <div class="relative">
                    <img src="${item.image}" alt="${item.title}" 
                         class="w-full h-64 object-cover" loading="lazy">
                    <div class="absolute top-4 left-4">
                        <span class="px-3 py-1 bg-black bg-opacity-50 text-white text-xs rounded-full">
                            ${this.portfolioData.categories[item.category]?.name || item.category}
                        </span>
                    </div>
                    <div class="absolute bottom-4 right-4">
                        <img src="${item.stylist.image}" alt="${item.stylistName}" 
                             class="w-8 h-8 rounded-full border-2 border-white shadow-lg">
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="font-bold text-lg mb-2 text-white">${item.title}</h3>
                    <p class="text-gray-300 text-sm mb-3 line-clamp-2">${item.description}</p>
                    <div class="flex items-center justify-between">
                        <span class="text-neon-cyan text-sm font-medium">by ${item.stylistDisplayName}</span>
                        <button class="btn-holographic px-3 py-1 text-xs rounded-lg stylist-profile-btn" 
                                data-stylist-id="${item.stylistId}">
                            View Profile
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    // Render list view
    renderListView(container, work) {
        container.className = 'space-y-4';
        
        container.innerHTML = work.map(item => `
            <div class="portfolio-item glass rounded-xl p-6 cursor-pointer transform transition-all duration-300 hover:scale-102" 
                 data-work-id="${item.id}">
                <div class="flex gap-6">
                    <img src="${item.image}" alt="${item.title}" 
                         class="w-32 h-32 object-cover rounded-lg flex-shrink-0" loading="lazy">
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-2">
                            <h3 class="font-bold text-xl text-white">${item.title}</h3>
                            <span class="px-3 py-1 bg-neon-purple bg-opacity-20 text-neon-purple text-xs rounded-full">
                                ${this.portfolioData.categories[item.category]?.name || item.category}
                            </span>
                        </div>
                        <p class="text-gray-300 mb-4">${item.description}</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <img src="${item.stylist.image}" alt="${item.stylistName}" 
                                     class="w-10 h-10 rounded-full border-2 border-neon-cyan">
                                <div>
                                    <p class="text-white font-medium">${item.stylistDisplayName}</p>
                                    <p class="text-gray-400 text-sm">${new Date(item.date).toLocaleDateString()}</p>
                                </div>
                            </div>
                            <button class="btn-holographic px-4 py-2 rounded-lg stylist-profile-btn" 
                                    data-stylist-id="${item.stylistId}">
                                View Profile
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    // Update active filter buttons
    updateActiveFilters() {
        // Update stylist filter buttons
        document.querySelectorAll('[data-stylist]').forEach(btn => {
            const stylistId = btn.dataset.stylist;
            if (stylistId === this.currentStylist || (stylistId === 'all' && !this.currentStylist)) {
                btn.classList.add('active', 'bg-neon-purple', 'text-white');
                btn.classList.remove('bg-white', 'bg-opacity-10');
            } else {
                btn.classList.remove('active', 'bg-neon-purple', 'text-white');
                btn.classList.add('bg-white', 'bg-opacity-10');
            }
        });

        // Update category filter buttons
        document.querySelectorAll('[data-category]').forEach(btn => {
            const category = btn.dataset.category;
            if (category === this.currentCategory) {
                btn.classList.add('active', 'bg-neon-cyan', 'text-white');
                btn.classList.remove('bg-white', 'bg-opacity-10');
            } else {
                btn.classList.remove('active', 'bg-neon-cyan', 'text-white');
                btn.classList.add('bg-white', 'bg-opacity-10');
            }
        });
    }

    // Update view buttons
    updateViewButtons() {
        document.querySelectorAll('[data-view]').forEach(btn => {
            const view = btn.dataset.view;
            if (view === this.currentView) {
                btn.classList.add('active', 'bg-neon-green', 'text-white');
            } else {
                btn.classList.remove('active', 'bg-neon-green', 'text-white');
            }
        });
    }

    // Update results count
    updateResultsCount(count) {
        const counter = document.getElementById('results-count');
        if (counter) {
            counter.textContent = `${count} work${count !== 1 ? 's' : ''} found`;
        }
    }

    // Show empty state
    renderEmptyState(container) {
        container.innerHTML = `
            <div class="col-span-full text-center py-16">
                <div class="glass rounded-xl p-8 max-w-md mx-auto">
                    <i class="fas fa-search text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-xl font-bold text-white mb-2">No Work Found</h3>
                    <p class="text-gray-300 mb-4">No portfolio items match your current filters.</p>
                    <button onclick="portfolioManager.clearFilters()" 
                            class="btn-holographic px-6 py-2 rounded-lg">
                        Clear Filters
                    </button>
                </div>
            </div>
        `;
    }

    // Clear all filters
    clearFilters() {
        this.currentStylist = 'all';
        this.currentCategory = 'all';
        this.updateActiveFilters();
        this.renderPortfolio();
        
        // Clear URL parameters
        const url = new URL(window.location);
        url.searchParams.delete('stylist');
        url.searchParams.delete('category');
        window.history.pushState({}, '', url);
    }

    // Get fallback data if JSON fails to load
    getFallbackData() {
        return {
            stylists: {
                sheryl_williams: {
                    id: "sheryl_williams",
                    name: "Sheryl Williams",
                    displayName: "Sheryl",
                    image: "assets/images/sw.png",
                    specialties: ["Locs", "Natural Hair"],
                    portfolio: {
                        featured_work: [
                            {
                                id: "sw_001",
                                title: "Goddess Locs",
                                description: "Beautiful goddess locs styling",
                                image: "assets/images/rs1.jpg",
                                category: "locs",
                                date: "2024-01-15"
                            }
                        ],
                        recent_work: []
                    }
                }
            },
            categories: {
                locs: { name: "Locs", color: "#8b5cf6" },
                braids: { name: "Braids", color: "#ec4899" },
                barber: { name: "Barber", color: "#10b981" }
            }
        };
    }

    // Show work detail in static modal (new preferred method)
    showWorkDetailInStaticModal(workId) {
        const allWork = this.getFilteredPortfolio();
        const work = allWork.find(w => w.id === workId);

        if (!work) return;

        // Prepare data for static modal
        const modalData = {
            src: work.image,
            alt: work.title || '',
            title: work.title || 'Portfolio Work',
            description: work.description || '',
            category: work.category || '',
            stylist: work.stylistDisplayName || work.stylistId || ''
        };

        // Call the global static modal function
        if (typeof window.openGalleryModal === 'function') {
            window.openGalleryModal(modalData);
        } else {
            console.warn('Static gallery modal function not available, falling back to dynamic modal');
            // Fallback to old system if needed
            this.showWorkDetail(workId);
        }
    }

    // Show work detail modal (kept for backward compatibility)
    showWorkDetail(workId) {
        console.warn('showWorkDetail is deprecated, use showWorkDetailInStaticModal instead');
        const allWork = this.getFilteredPortfolio();
        const work = allWork.find(w => w.id === workId);

        if (!work) return;

        const modal = this.createWorkDetailModal(work);
        document.body.appendChild(modal);

        // Show modal with animation
        setTimeout(() => {
            modal.classList.remove('opacity-0');
            modal.querySelector('.modal-content').classList.remove('scale-95');
        }, 10);
    }

    // Create work detail modal
    createWorkDetailModal(work) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 opacity-0 transition-opacity duration-300';
        modal.innerHTML = `
            <div class="modal-content glass rounded-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto transform scale-95 transition-transform duration-300">
                <div class="relative">
                    <button class="absolute top-4 right-4 text-white hover:text-neon-cyan z-10" onclick="this.closest('.fixed').remove()">
                        <i class="fas fa-times text-2xl"></i>
                    </button>

                    <div class="grid md:grid-cols-2 gap-8 p-8">
                        <div>
                            <img src="${work.image}" alt="${work.title}" class="w-full rounded-xl shadow-2xl">
                        </div>

                        <div class="space-y-6">
                            <div>
                                <span class="px-3 py-1 bg-neon-purple bg-opacity-20 text-neon-purple text-sm rounded-full">
                                    ${this.portfolioData.categories[work.category]?.name || work.category}
                                </span>
                                <h2 class="text-3xl font-bold text-white mt-4 mb-2">${work.title}</h2>
                                <p class="text-gray-300 text-lg">${work.description}</p>
                            </div>

                            <div class="flex items-center gap-4 p-4 glass rounded-xl">
                                <img src="${work.stylist.image}" alt="${work.stylistName}"
                                     class="w-16 h-16 rounded-full border-2 border-neon-cyan">
                                <div>
                                    <h3 class="text-xl font-bold text-white">${work.stylistName}</h3>
                                    <p class="text-neon-cyan">${work.stylist.specialties.join(', ')}</p>
                                    <p class="text-gray-400 text-sm">${new Date(work.date).toLocaleDateString()}</p>
                                </div>
                            </div>

                            <div class="space-y-3">
                                <h4 class="text-lg font-bold text-white">Tags</h4>
                                <div class="flex flex-wrap gap-2">
                                    ${work.tags.map(tag => `
                                        <span class="px-3 py-1 bg-white bg-opacity-10 text-white text-sm rounded-full">
                                            #${tag}
                                        </span>
                                    `).join('')}
                                </div>
                            </div>

                            <div class="flex gap-4">
                                <button onclick="openSquareBooking()" class="btn-holographic flex-1 py-3 rounded-lg">
                                    <i class="fas fa-calendar-plus mr-2"></i>Book with ${work.stylistDisplayName}
                                </button>
                                <button class="btn-holographic px-6 py-3 rounded-lg stylist-profile-btn"
                                        data-stylist-id="${work.stylistId}">
                                    <i class="fas fa-user mr-2"></i>View Profile
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        return modal;
    }

    // Show stylist profile modal
    showStylistProfile(stylistId) {
        const stylist = this.portfolioData.stylists[stylistId];
        if (!stylist) return;

        const modal = this.createStylistProfileModal(stylist);
        document.body.appendChild(modal);

        // Show modal with animation
        setTimeout(() => {
            modal.classList.remove('opacity-0');
            modal.querySelector('.modal-content').classList.remove('scale-95');
        }, 10);
    }

    // Create stylist profile modal
    createStylistProfileModal(stylist) {
        const allWork = [...(stylist.portfolio.featured_work || []), ...(stylist.portfolio.recent_work || [])];

        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 opacity-0 transition-opacity duration-300';
        modal.innerHTML = `
            <div class="modal-content glass rounded-2xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-y-auto transform scale-95 transition-transform duration-300">
                <div class="relative p-8">
                    <button class="absolute top-4 right-4 text-white hover:text-neon-cyan z-10" onclick="this.closest('.fixed').remove()">
                        <i class="fas fa-times text-2xl"></i>
                    </button>

                    <!-- Stylist Header -->
                    <div class="text-center mb-8">
                        <img src="${stylist.image}" alt="${stylist.name}"
                             class="w-32 h-32 rounded-full mx-auto mb-4 border-4 border-neon-cyan shadow-2xl">
                        <h2 class="text-4xl font-bold text-white mb-2">${stylist.name}</h2>
                        <div class="flex justify-center gap-2 mb-4">
                            ${stylist.specialties.map(specialty => `
                                <span class="px-4 py-2 bg-neon-purple bg-opacity-20 text-neon-purple rounded-full">
                                    ${specialty}
                                </span>
                            `).join('')}
                        </div>
                        <p class="text-gray-300 text-lg max-w-2xl mx-auto">${stylist.bio}</p>
                    </div>

                    <!-- Portfolio Grid -->
                    <div class="mb-8">
                        <h3 class="text-2xl font-bold text-white mb-6 text-center">Portfolio</h3>
                        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
                            ${allWork.map(work => `
                                <div class="glass rounded-xl overflow-hidden cursor-pointer transform transition-all duration-300 hover:scale-105"
                                     onclick="portfolioManager.showWorkDetail('${work.id}')">
                                    <img src="${work.image}" alt="${work.title}" class="w-full h-48 object-cover">
                                    <div class="p-4">
                                        <h4 class="font-bold text-white mb-2">${work.title}</h4>
                                        <p class="text-gray-300 text-sm">${work.description}</p>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <!-- Book Button -->
                    <div class="text-center">
                        <button onclick="openSquareBooking()" class="btn-holographic px-8 py-4 rounded-lg text-lg">
                            <i class="fas fa-calendar-plus mr-3"></i>Book Appointment with ${stylist.displayName}
                        </button>
                    </div>
                </div>
            </div>
        `;

        return modal;
    }

    // Handle errors
    handleError(error) {
        console.error('Portfolio Manager Error:', error);
        const container = document.getElementById('portfolio-container') ||
                         document.getElementById('gallery-grid');

        if (container) {
            container.innerHTML = `
                <div class="col-span-full text-center py-16">
                    <div class="glass rounded-xl p-8 max-w-md mx-auto">
                        <i class="fas fa-exclamation-triangle text-4xl text-red-400 mb-4"></i>
                        <h3 class="text-xl font-bold text-white mb-2">Error Loading Portfolio</h3>
                        <p class="text-gray-300 mb-4">Unable to load stylist portfolio data.</p>
                        <button onclick="location.reload()"
                                class="btn-holographic px-6 py-2 rounded-lg">
                            Retry
                        </button>
                    </div>
                </div>
            `;
        }
    }
}

// Initialize portfolio manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.portfolioManager = new StylistPortfolioManager();
});

// Global functions for backward compatibility
window.filterByStylist = (stylistId) => {
    if (window.portfolioManager) {
        window.portfolioManager.filterByStylist(stylistId);
    }
};

window.filterByCategory = (category) => {
    if (window.portfolioManager) {
        window.portfolioManager.filterByCategory(category);
    }
};
